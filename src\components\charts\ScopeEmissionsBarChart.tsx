import React, { useEffect, useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import axios from "axios";
import { Skeleton } from "../ui/skeleton";

interface ScopeEmssionsProps {
  suppliersData: any;
}

const regionOptions = ["India", "Indonesia", "United Kingdom", "Global"];

// Sample data for each region and scope
const regionData: Record<
  string,
  { scope1: number[]; scope2: number[]; scope3: number[] }
> = {
  India: {
    scope1: [],
    scope2: [],
    scope3: [],
  },
  Indonesia: {
    scope1: [],
    scope2: [],
    scope3: [],
  },
  "United Kingdom": {
    scope1: [],
    scope2: [],
    scope3: [],
  },
  Global: {
    scope1: [],
    scope2: [],
    scope3: [],
  },
};

export const ScopeEmissionsBarChart = ({
  suppliersData,
}: ScopeEmssionsProps) => {
  const [selectedRegion, setSelectedRegion] = useState("Global");
  const [supplierData, setSupplierData] = useState<any[]>(suppliersData);
  const [locationData, setLocationData] = useState<any[]>([]);
  const [scopeData, setScopeData] = useState<any>({});

  const data = regionData[selectedRegion] || regionData["Global"];

  const fetchLocationData: any = async (location: string) => {
    try {
      const response = await axios.get(
        "https://api.eisqr.com/user-profiles/289/location-ones?filter=%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationTwos%22%2C%22scope%22%3A%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationThrees%22%7D%5D%7D%7D%5D%7D"
      );

      return response.data;
    } catch (err) {
      console.warn("error:", err);
    }
  };

  const extractAllLocationIds = (locationOne: any): number[] => {
    const ids: number[] = [];
    ids.push(locationOne.id);

    locationOne.locationTwos?.forEach((locationTwo: any) => {
      if (locationTwo.id) ids.push(locationTwo.id);

      locationTwo.locationThrees?.forEach((locationThree: any) => {
        if (locationThree.id) ids.push(locationThree.id);
      });
    });

    return ids;
  };

  useEffect(() => {
    (async () => {
      setSelectedRegion("Global");
      const locationData = await fetchLocationData(selectedRegion);
      setLocationData(locationData);

      setSupplierData(suppliersData);
    })();
  }, []);

  useEffect(() => {
    (async () => {
      let locationIndvData =
        selectedRegion == "Global"
          ? locationData
          : locationData.filter((item) => item.name == selectedRegion)[0];

      const allLocationIds = extractAllLocationIds(locationIndvData);

      let finalData =
        selectedRegion == "Global"
          ? supplierData
          : supplierData.filter((item) =>
              allLocationIds.includes(item.locationId)
            );

      let scope1 = { 2024: 0, 2025: 0, 2026: 0 };
      let scope2 = { 2024: 0, 2025: 0, 2026: 0 };
      let scope3 = { 2024: 0, 2025: 0, 2026: 0 };

      finalData.forEach((item) => {
        const period = item.reporting_period;
        let fiscalYear = "";

        if (period.includes("to")) {
          const match = period.match(/Apr-(\d{4}) to Mar-(\d{4})/);
          if (match) fiscalYear = match[2];
        } else {
          const match = period.match(/([A-Za-z]{3}|\d{2})-(\d{4})/);
          if (match) {
            const monthStr = match[1];
            const year = +match[2];
            const monthMap: Record<string, number> = {
              Jan: 0,
              Feb: 1,
              Mar: 2,
              Apr: 3,
              May: 4,
              Jun: 5,
              Jul: 6,
              Aug: 7,
              Sep: 8,
              Oct: 9,
              Nov: 10,
              Dec: 11,
            };
            const monthNum = isNaN(+monthStr)
              ? monthMap[monthStr]
              : +monthStr - 1;
            fiscalYear =
              monthNum >= 3 ? (year + 1).toString() : year.toString();
          }
        }

        if (!fiscalYear) return;

        if (item.indicatorId == 162) {
          const val = parseFloat(item.computedValue);
          scope1[fiscalYear] += isNaN(val) ? 0 : val;
        } else if (item.indicatorId == 163) {
          scope2[fiscalYear] += +item.value || 0;
        } else {
          scope3[fiscalYear] += +item.value || 0;
        }
      });

      data["scope1"] = Object.values(scope1).map((item) => +item.toFixed(2));
      data["scope2"] = Object.values(scope2).map((item) => +item.toFixed(2));
      data["scope3"] = Object.values(scope3).map((item) => +item.toFixed(2));

      setScopeData(data);
    })();
  }, [selectedRegion]);

  const options: Highcharts.Options = {
    chart: {
      type: "column",
      backgroundColor: "#fff",
      style: { fontFamily: "inherit" },
    },
    title: { text: undefined },
    xAxis: {
      categories: ["FY-2024", "FY-2025", "FY-2026"],
      title: { text: "Fiscal Year" },
      labels: { style: { fontSize: "14px", color: "#334155" } },
    },
    yAxis: {
      min: 0,
      title: {
        text: "Emissions (Million tCO₂e)",
        style: { fontSize: "14px", color: "#334155" },
      },
      labels: {
        formatter: function () {
          return this.value + "";
        },
        style: { fontSize: "13px", color: "#64748b" },
      },
      gridLineColor: "#e5e7eb",
    },
    legend: { enabled: true },
    tooltip: {
      shared: true,
      headerFormat: "<b>{point.key}</b><br/>",
      pointFormat:
        '<span style="color:{series.color}">{series.name}</span>: <b>{point.y} Million tCO₂e</b><br/>',
    },
    plotOptions: {
      column: {
        grouping: true,
        borderRadius: 6,
        pointPadding: 0.1,
        groupPadding: 0.15,
        dataLabels: {
          enabled: true,
          format: "{y}",
          style: { fontWeight: "bold", color: "#334155" },
        },
      },
    },
    series: [
      {
        type: "column",
        name: "Scope 1",
        data: scopeData.scope1,
        color: "#3b82f6",
      },
      {
        type: "column",
        name: "Scope 2",
        data: scopeData.scope2,
        color: "#10b981",
      },
      {
        type: "column",
        name: "Scope 3",
        data: scopeData.scope3,
        color: "#f59e0b",
      },
    ],
    credits: { enabled: false },
  };

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="mb-4 flex items-center space-x-3">
        <label
          htmlFor="scope-emissions-region-select"
          className="font-medium text-gray-700"
        >
          Country/Region:
        </label>
        <select
          id="scope-emissions-region-select"
          value={selectedRegion}
          onChange={(e) => setSelectedRegion(e.target.value)}
          className="border border-gray-300 rounded px-3 py-1.5 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-400 bg-white shadow-sm"
        >
          {regionOptions.map((opt) => (
            <option key={opt} value={opt}>
              {opt}
            </option>
          ))}
        </select>
      </div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};
