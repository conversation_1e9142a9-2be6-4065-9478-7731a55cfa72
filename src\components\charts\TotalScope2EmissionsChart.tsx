import React, { useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

interface TotalScope2EmissionsChartProps {
  region?: string;
  enableDrilldown?: boolean;
  useLocationFilter?: boolean;
  data?: any[];
  year?: number;
}

const TotalScope2EmissionsChart: React.FC<TotalScope2EmissionsChartProps> = ({
  region = "Global",
  enableDrilldown = false,
  useLocationFilter = false,
  data = [],
  year = 2024
}) => {
  console.log("TotalScope2 Data:", data);
  console.log("Year:", year);

  // Process real data for Total Scope 2 Emissions
  const scope2Data = React.useMemo(() => {
    console.log("Processing data for year:", year);
    console.log("Raw data:", data);

    // Filter data for the specified year and Scope 2 indicator (169)
    const yearData = data.filter(item =>
      item.reporting_period &&
      item.reporting_period.includes(`-${year}`) &&
      item.indicatorId === 169
    );

    console.log("Filtered year data:", yearData);

    // Group data by reporting_period and sum computedValue
    const periodGroups = yearData.reduce((acc, item) => {
      const period = item.reporting_period;

      if (!acc[period]) {
        acc[period] = {
          period: period,
          value: 0,
          recordCount: 0,
          items: [],
          gridFactor: parseFloat(item.emissionFactorValue) || 0
        };
      }

      acc[period].value += parseFloat(item.computedValue) || 0;
      acc[period].recordCount += 1;
      acc[period].items.push(item);

      return acc;
    }, {});

    console.log("Period groups:", periodGroups);

    // Convert to array and sort by month order
    const monthOrder = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

    const monthlyData = Object.values(periodGroups)
      .map((group: any) => {
        // Extract month from period (e.g., "Jan-2024" -> "Jan")
        const month = group.period.split('-')[0];
        const monthIndex = monthOrder.indexOf(month);

        return {
          month: month,
          value: group.value,
          period: group.period,
          recordCount: group.recordCount,
          monthIndex: monthIndex >= 0 ? monthIndex : 999, // Put unknown months at end
          items: group.items,
          gridFactor: group.gridFactor
        };
      })
      .sort((a, b) => a.monthIndex - b.monthIndex); // Sort by month order

    console.log("Processed monthly data:", monthlyData);

    // Calculate totals
    const current = monthlyData.reduce((sum, item) => sum + item.value, 0);
    const previousYear = current * 1.1; // Assume previous year was 10% higher

    return {
      current,
      previousYear,
      monthlyData,
      year,
      periodsWithData: monthlyData.length
    };
  }, [data, year]);



  const chartOptions = {
    chart: {
      type: 'column',
      backgroundColor: 'transparent',
      height: 400,
    },
    title: {
      text: `Total Scope 2 Emissions (${year})`,
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    subtitle: {
      text: ``,
      style: {
        fontSize: '14px',
        color: '#6b7280'
      }
    },
    xAxis: {
      categories: scope2Data.monthlyData.map(item => item.period),
      title: {
        text: 'Reporting Period'
      },
      labels: {
        rotation: -45,
        style: {
          fontSize: '11px'
        }
      }
    },
    yAxis: [{
      title: {
        text: 'Emissions (tCO₂e)',
        style: {
          color: '#3b82f6'
        }
      },
    }],
    series: [{
      name: 'Scope 2 Emissions',
      type: 'column',
      yAxis: 0,
      data: scope2Data.monthlyData.map((item, index) => ({
        y: item.value,
        color: '#3b82f6' // Consistent blue color
      })),
      dataLabels: {
        enabled: true,
        format: '{y:.1f}'
      }
    }],
    plotOptions: {
      column: {
        borderRadius: 4,
        dataLabels: {
          enabled: true
        }
      }
    },
    tooltip: {
      formatter: function() {
        const periodData = scope2Data.monthlyData.find(m => m.period === this.x);
    const monthOrder = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

        return `<b>${monthOrder[this.x]}</b><br/>
                Emissions: <b>${this.y.toFixed(2)} tCO₂e</b><br/>
                Data Records: <b>${periodData?.recordCount || 0}</b>`;
      }
    },
    legend: {
      enabled: true
    },
    credits: {
      enabled: false
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="mb-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Total Scope 2 Emissions ({year})</h3>
            <p className="text-sm text-gray-600">Indirect emissions from purchased electricity</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">{scope2Data.current.toFixed(2)} tCO₂e</div>
            <div className="text-sm text-gray-500">
              Total emissions for {year}
            </div>
          </div>
        </div>
      </div>
      
      <HighchartsReact
        highcharts={Highcharts}
        options={chartOptions}
      />
      
      <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
        <div className="text-center">
          <div className="font-semibold text-gray-900">{scope2Data.current.toFixed(2)}</div>
          <div className="text-gray-500">Total {year}</div>
        </div>
        <div className="text-center">
          <div className="font-semibold text-gray-900">
            {((scope2Data.current - scope2Data.previousYear) / scope2Data.previousYear * 100).toFixed(1)}%
          </div>
          <div className="text-gray-500">vs Previous Year</div>
        </div>
        <div className="text-center">
          <div className="font-semibold text-gray-900">
            {scope2Data.periodsWithData}
          </div>
          <div className="text-gray-500">Reporting Periods</div>
        </div>
      </div>

      {/* No Data Message */}
      {scope2Data.periodsWithData === 0 && (
        <div className="mt-4 text-center py-4 bg-yellow-50 rounded-lg">
          <div className="text-yellow-700">
            No Scope 2 emissions data found for {year}.
            <br />
            <span className="text-sm">
              Ensure data contains records with indicatorId 169 and reporting_period containing "-{year}"
            </span>
          </div>
        </div>
      )}


    </div>
  );
};

export default TotalScope2EmissionsChart;
