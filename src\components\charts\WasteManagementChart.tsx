import React, { useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { EnvironmentDrilldownChart } from "./EnvironmentDrilldownChart";
import wasteMonthlyDrilldownData from "@/data/waste-monthly-drilldown-data.json";
import { EnvironmentLocationFilter, EnvironmentLocation } from "./EnvironmentLocationFilter";

interface WasteManagementChartProps {
  region: string;
  enableDrilldown?: boolean;
  useLocationFilter?: boolean; // true for Environment tab, false for Overview tab
}

const regionOptions = ["India", "Indonesia", "United Kingdom", "Global"];

// Sample data: region -> { hazardous: [FY-2024, FY-2025, FY-2026], nonHazardous: [FY-2024, FY-2025, FY-2026] }
const regionData: Record<string, { hazardous: number[]; nonHazardous: number[] }> = {
  India: {
    hazardous: [0.8, 0.7, 0.6],
    nonHazardous: [2.5, 2.3, 2.1],
  },
  Indonesia: {
    hazardous: [0.6, 0.5, 0.5],
    nonHazardous: [2.0, 1.9, 1.8],
  },
  "United Kingdom": {
    hazardous: [0.3, 0.3, 0.2],
    nonHazardous: [1.2, 1.1, 1.0],
  },
  Global: {
    hazardous: [2.0, 1.8, 1.6],
    nonHazardous: [6.0, 5.7, 5.4],
  },
};

export const WasteManagementChart: React.FC<WasteManagementChartProps> = ({ region, enableDrilldown = false, useLocationFilter = false }) => {
  const [location, setLocation] = useState<EnvironmentLocation>({ country: region || "Global", region: "", businessUnit: "" });
  const [selectedRegion, setSelectedRegion] = useState(region || "Global");
  const [drilldown, setDrilldown] = useState<{ isActive: boolean; category: string; year: string } | null>(null);
  // Data selection logic: prefer businessUnit > region > country
  let data = useLocationFilter
    ? (regionData[location.country] || regionData["Global"])
    : (regionData[selectedRegion] || regionData["Global"]);
  // (Extend here if you add region/businessUnit-level data)

  const handleDrilldown = (metricType: string, yearIndex: number) => {
    if (!enableDrilldown) return;
    const years = ["2024", "2025", "2026"];
    setDrilldown({ isActive: true, category: metricType, year: years[yearIndex] });
  };

  const handleBack = () => setDrilldown(null);

  if (enableDrilldown && drilldown?.isActive) {
    return (
      <EnvironmentDrilldownChart
        metricType={drilldown.category}
        region={location.country}
        year={drilldown.year}
        onBack={handleBack}
        monthlyDrilldownData={wasteMonthlyDrilldownData.monthlyDrilldownData}
      />
    );
  }

  const options: Highcharts.Options = {
    chart: {
      type: "column",
      backgroundColor: "#fff",
      style: { fontFamily: 'inherit' },
    },
    title: { text: undefined },
    xAxis: {
      categories: ["FY-2024", "FY-2025", "FY-2026"],
      title: { text: "Fiscal Year" },
      labels: { style: { fontSize: "14px", color: "#334155" } },
    },
    yAxis: {
      min: 0,
      title: {
        text: "Waste (Million Tons, MT)",
        style: { fontSize: "14px", color: "#334155" },
      },
      labels: {
        formatter: function () { return this.value + ""; },
        style: { fontSize: "13px", color: "#64748b" },
      },
      gridLineColor: "#e5e7eb",
    },
    legend: { enabled: true },
    tooltip: {
      shared: true,
      headerFormat: '<b>{point.key}</b><br/>',
      pointFormat: '<span style="color:{series.color}">{series.name}</span>: <b>{point.y} MT</b><br/>'
    },
    plotOptions: {
      column: {
        grouping: true,
        borderRadius: 6,
        pointPadding: 0.1,
        groupPadding: 0.15,
        dataLabels: {
          enabled: true,
          format: "{y}",
          style: { fontWeight: "bold", color: "#334155" },
        },
        point: {
          events: {
            click: function () {
              if (!enableDrilldown) return;
              const metricType = this.series.name.toLowerCase().includes("hazardous") ? (this.series.name.toLowerCase().includes("non") ? "nonHazardous" : "hazardous") : "hazardous";
              handleDrilldown(metricType, this.x);
            }
          }
        },
        cursor: enableDrilldown ? 'pointer' : 'default',
      },
    },
    series: [
      {
        type: "column",
        name: "Hazardous Waste",
        data: data.hazardous,
        color: "#ef4444",
      },
      {
        type: "column",
        name: "Non-Hazardous Waste",
        data: data.nonHazardous,
        color: "#10b981",
      },
    ],
    credits: { enabled: false },
  };

  return (
    <div className="bg-white rounded-lg shadow p-4">
      {useLocationFilter ? (
        <EnvironmentLocationFilter value={location} onChange={setLocation} />
      ) : (
        <div className="mb-4 flex items-center space-x-3">
          <label htmlFor="waste-region-select" className="font-medium text-gray-700">Country/Region:</label>
          <select
            id="waste-region-select"
            value={selectedRegion}
            onChange={e => setSelectedRegion(e.target.value)}
            className="border border-gray-300 rounded px-3 py-1.5 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-400 bg-white shadow-sm"
          >
            {Object.keys(regionData).map(opt => (
              <option key={opt} value={opt}>{opt}</option>
            ))}
          </select>
        </div>
      )}
      {enableDrilldown && (
        <div className="mb-2 text-sm text-gray-600">
          💡 Click on any bar to view monthly breakdown
        </div>
      )}
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
}; 