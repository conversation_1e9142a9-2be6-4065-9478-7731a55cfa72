import React, { useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

interface TotalScope1EmissionsChartProps {
  region?: string;
  enableDrilldown?: boolean;
  useLocationFilter?: boolean;
  data?: any[];
  year?: number;
}

const TotalScope1EmissionsChart: React.FC<TotalScope1EmissionsChartProps> = ({
  region = "Global",
  enableDrilldown = false,
  useLocationFilter = false,
  data = [],
  year = 2024
}) => {
  console.log("TotalScope1 Data:", data);
  console.log("Year:", year);

  // Process real data for Total Scope 1 Emissions
  const scope1Data = React.useMemo(() => {
    console.log("Processing data for year:", year);
    console.log("Raw data:", data);

    // Filter data for the specified year and Scope 1 indicators
    const yearData = data.filter(item =>
      item.reporting_period &&
      item.reporting_period.includes(`-${year}`) &&
      (item.indicatorId === 826 || item.indicatorId === 287)
    );

    console.log("Filtered year data:", yearData);

    // Group data by reporting_period and sum computedValue
    const periodGroups = yearData.reduce((acc, item) => {
      const period = item.reporting_period;

      if (!acc[period]) {
        acc[period] = {
          period: period,
          value: 0,
          recordCount: 0,
          items: []
        };
      }

      acc[period].value += parseFloat(item.computedValue) || 0;
      acc[period].recordCount += 1;
      acc[period].items.push(item);

      return acc;
    }, {});

    console.log("Period groups:", periodGroups);

    // Convert to array and sort by month order
    const monthOrder = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

    const monthlyData = Object.values(periodGroups)
      .map((group: any) => {
        // Extract month from period (e.g., "Jan-2024" -> "Jan")
        const month = group.period.split('-')[0];
        const monthIndex = monthOrder.indexOf(month);

        return {
          month: month,
          value: group.value,
          period: group.period,
          recordCount: group.recordCount,
          monthIndex: monthIndex >= 0 ? monthIndex : 999, // Put unknown months at end
          items: group.items
        };
      })
      .sort((a, b) => a.monthIndex - b.monthIndex); // Sort by month order

    console.log("Processed monthly data:", monthlyData);

    // Calculate totals
    const current = monthlyData.reduce((sum, item) => sum + item.value, 0);
    const previousYear = current * 1.1; // Assume previous year was 10% higher

    return {
      current,
      previousYear,
      monthlyData,
      year,
      periodsWithData: monthlyData.length
    };
  }, [data, year]);

  const chartOptions = {
    chart: {
      type: 'column',
      backgroundColor: 'transparent',
      height: 400,
    },
    title: {
      text: `Total Scope 1 Emissions (${year})`,
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    subtitle: {
      text: ``,
      style: {
        fontSize: '14px',
        color: '#6b7280'
      }
    },
    xAxis: {
      categories: scope1Data.monthlyData.map(item => item.period),
      title: {
        text: 'Reporting Period'
      },
      labels: {
        rotation: -45,
        style: {
          fontSize: '11px'
        }
      }
    },
    yAxis: {
      title: {
        text: 'Emissions (tCO₂e)'
      },
      plotLines: [{
        value: scope1Data.target / 12, // Monthly target
        color: '#ef4444',
        dashStyle: 'dash',
        width: 2,
        label: {
          text: 'Monthly Target',
          align: 'right',
          style: {
            color: '#ef4444'
          }
        }
      }]
    },
    series: [{
      name: 'Scope 1 Emissions',
      data: scope1Data.monthlyData.map(item => ({
        y: item.value,
        color: '#10b981' // Consistent green color
      })),
      dataLabels: {
        enabled: true,
        format: '{y:.1f}'
      }
    }],
    plotOptions: {
      column: {
        borderRadius: 4,
        dataLabels: {
          enabled: true
        }
      }
    },
    tooltip: {
      formatter: function() {
        const periodData = scope1Data.monthlyData.find(m => m.period === this.x);
    const monthOrder = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

        return `<b>${monthOrder[this.x]}</b><br/>
                Emissions: <b>${this.y.toFixed(2)} tCO₂e</b><br/>
                `;
      }
    },
    legend: {
      enabled: false
    },
    credits: {
      enabled: false
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="mb-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Total Scope 1 Emissions ({year})</h3>
            <p className="text-sm text-gray-600">Direct emissions from owned or controlled sources</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">{scope1Data.current.toFixed(2)} tCO₂e</div>
            <div className="text-sm text-gray-500">
              Total emissions for {year}
            </div>
          </div>
        </div>
      </div>
      
      <HighchartsReact
        highcharts={Highcharts}
        options={chartOptions}
      />
      


      {/* No Data Message */}
      {scope1Data.periodsWithData === 0 && (
        <div className="mt-4 text-center py-4 bg-yellow-50 rounded-lg">
          <div className="text-yellow-700">
            No Scope 1 emissions data found for {year}.
            <br />
          
          </div>
        </div>
      )}

      {/* Data Summary */}
     
    </div>
  );
};

export default TotalScope1EmissionsChart;
