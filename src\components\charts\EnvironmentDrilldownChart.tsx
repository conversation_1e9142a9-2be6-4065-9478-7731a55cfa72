import React, { useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { ArrowLeft } from "lucide-react";
import defaultMonthlyDrilldownData from "@/data/environment-monthly-drilldown-data.json";

interface EnvironmentDrilldownChartProps {
  metricType: string;
  country: string;
  region: string;
  businessUnit: string;
  year: string;
  category?: string;
  onBack: () => void;
  monthlyDrilldownData?: any;
}

export const EnvironmentDrilldownChart: React.FC<EnvironmentDrilldownChartProps> = ({
  metricType,
  country,
  region,
  businessUnit,
  year,
  category,
  onBack,
  monthlyDrilldownData
}) => {
  // Use prop or default
  const drilldownData = monthlyDrilldownData || defaultMonthlyDrilldownData.monthlyDrilldownData;

  // Move these functions above the options object
  const getMetricName = (type: string, cat?: string) => {
    if (cat) return cat;
    switch (type) {
      case "scope1": return "Scope 1 Emissions";
      case "scope2": return "Scope 2 Emissions";
      case "scope3": return "Scope 3 Emissions";
      case "renewable": return "Renewable Energy Consumption";
      case "nonrenewable": return "Non-Renewable Energy Consumption";
      default: return "Emissions";
    }
  };

  const getMetricColor = (type: string, cat?: string) => {
    if (cat) return "#8b5cf6"; // Purple for categories
    switch (type) {
      case "scope1": return "#3b82f6";
      case "scope2": return "#10b981";
      case "scope3": return "#f59e0b";
      case "renewable": return "#10b981";
      case "nonrenewable": return "#f59e0b";
      default: return "#6b7280";
    }
  };

  // Helper to get the most granular key
  const getGranularKey = () => {
    if (businessUnit) return businessUnit;
    if (region) return region;
    return country;
  };

  // Get the monthly data based on metric type and category
  const getMonthlyData = () => {
    // Try businessUnit, then region, then country
    const keysToTry = [businessUnit, region, country].filter(Boolean);
    if (category && metricType === "scope3" && drilldownData.scope3Categories) {
      for (const key of keysToTry) {
        const data = drilldownData.scope3Categories[category]?.[key]?.[year];
        if (data && data.length) return data;
      }
      return [];
    } else {
      for (const key of keysToTry) {
        const data = drilldownData[metricType]?.[key]?.[year];
        if (data && data.length) return data;
      }
      return [];
    }
  };

  const monthlyData = getMonthlyData();
  const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

  const options: Highcharts.Options = {
    chart: {
      type: "column",
      backgroundColor: "#fff",
      style: { fontFamily: 'inherit' }
    },
    title: { text: undefined },
    xAxis: {
      categories: months,
      title: { text: "Month" },
      labels: { style: { fontSize: "14px", color: "#334155" } }
    },
    yAxis: {
      min: 0,
      title: {
        text: metricType === "renewable" || metricType === "nonrenewable" ? "Energy Consumption (GJ)" : "Emissions (t CO₂e)",
        style: { fontSize: "14px", color: "#334155" }
      },
      labels: {
        formatter: function () { return this.value + ""; },
        style: { fontSize: "13px", color: "#64748b" }
      },
      gridLineColor: "#e5e7eb"
    },
    legend: { enabled: false },
    tooltip: {
      headerFormat: '<b>{point.key}</b><br/>',
      pointFormat: `<span style="color:{series.color}">{series.name}</span>: <b>{point.y} ${metricType === "renewable" || metricType === "nonrenewable" ? "GJ" : "t CO₂e"}</b>`
    },
    plotOptions: {
      column: {
        borderRadius: 6,
        pointPadding: 0.1,
        groupPadding: 0.15,
        dataLabels: {
          enabled: true,
          format: "{y}",
          style: { fontWeight: "bold", color: "#334155", fontSize: "12px" }
        },
        color: getMetricColor(metricType, category)
      }
    },
    series: [
      {
        type: "column",
        name: getMetricName(metricType, category),
        data: monthlyData,
      }
    ],
    credits: { enabled: false }
  };

  const getContextTitle = () => {
    let location = businessUnit || region || country;
    if (category) {
      return `${category} - ${location} - ${year}`;
    }
    return `${getMetricName(metricType, category)} - ${location} - ${year}`;
  };

  return (
    <div className="bg-white rounded-lg shadow p-4">
      {/* Header with back button and context */}
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={onBack}
          className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
        >
          <ArrowLeft size={16} />
          <span>Back</span>
        </button>
        
        <div className="text-right">
          <h3 className="text-lg font-semibold text-gray-900">{getContextTitle()}</h3>
          <p className="text-sm text-gray-600">Monthly breakdown for {businessUnit || region || country}</p>
        </div>
      </div>

      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
}; 