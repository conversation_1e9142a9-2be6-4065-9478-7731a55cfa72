import React, { useEffect, useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { EnvironmentDrilldownChart } from "./EnvironmentDrilldownChart";
import waterMonthlyDrilldownData from "@/data/water-monthly-drilldown-data.json";
import {
  EnvironmentLocationFilter,
  EnvironmentLocation,
} from "./EnvironmentLocationFilter";
import axios from "axios";

interface WaterWithdrawalChartProps {
  region: string;
  suppliersData: any[];
  enableDrilldown?: boolean;
}

const regionOptions = ["India", "Indonesia", "United Kingdom", "Global"];
const sources = ["Surface Water", "Ground Water", "Third Party Water"];

// Sample data: region -> source -> [FY-2024, FY-2025, FY-2026]
const regionData: Record<string, Record<string, number[]>> = {
  India: {
    "Surface Water": [12000, 11500, 11000],
    "Ground Water": [8000, 7800, 7600],
    "Third Party Water": [4000, 3900, 3800],
  },
  Indonesia: {
    "Surface Water": [9000, 8800, 8600],
    "Ground Water": [6000, 5900, 5800],
    "Third Party Water": [3000, 2950, 2900],
  },
  "United Kingdom": {
    "Surface Water": [7000, 6900, 6800],
    "Ground Water": [4000, 3950, 3900],
    "Third Party Water": [2000, 1980, 1960],
  },
  Global: {
    "Surface Water": [30000, 29500, 29000],
    "Ground Water": [20000, 19800, 19600],
    "Third Party Water": [10000, 9900, 9800],
  },
};

export const WaterWithdrawalChart: React.FC<WaterWithdrawalChartProps> = ({
  region,
  suppliersData,
  enableDrilldown = false,
}) => {
  const [location, setLocation] = useState<EnvironmentLocation>({
    country: region || "Global",
    region: "",
    businessUnit: "",
  });
  const [selectedRegion, setSelectedRegion] = useState("Global");
  const [drilldown, setDrilldown] = useState<{
    isActive: boolean;
    source: string;
    year: string;
  } | null>(null);
  // Data selection logic: prefer businessUnit > region > country
  let data = regionData[location.country] || regionData["Global"];
  // (Extend here if you add region/businessUnit-level data)

  const [supplierData, setSupplierData] = useState<any[]>(suppliersData);
  const [locationData, setLocationData] = useState<any[]>([]);
  const [scopeData, setScopeData] = useState<any>({});

  const fetchLocationData: any = async (location: string) => {
    try {
      const response = await axios.get(
        "https://api.eisqr.com/user-profiles/289/location-ones?filter=%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationTwos%22%2C%22scope%22%3A%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationThrees%22%7D%5D%7D%7D%5D%7D"
      );

      return response.data;
    } catch (err) {
      console.warn("error:", err);
    }
  };

  const extractAllLocationIds = (locationOne: any): number[] => {
    const ids: number[] = [];
    ids.push(locationOne.id);

    locationOne.locationTwos?.forEach((locationTwo: any) => {
      if (locationTwo.id) ids.push(locationTwo.id);

      locationTwo.locationThrees?.forEach((locationThree: any) => {
        if (locationThree.id) ids.push(locationThree.id);
      });
    });

    return ids;
  };

  useEffect(() => {
    (async () => {
      setSelectedRegion("Global");
      const locationData = await fetchLocationData(selectedRegion);
      setLocationData(locationData);

      setSupplierData(suppliersData);
    })();
  }, []);

  useEffect(() => {
    (async () => {
      let locationIndvData =
        location.country == "Global"
          ? locationData
          : locationData.filter((item) => item.name == location.country)[0];

      const allLocationIds = extractAllLocationIds(locationIndvData);

      let finalData =
        location.country == "Global"
          ? supplierData
          : supplierData.filter((item) =>
              allLocationIds.includes(item.locationId)
            );

      let ground = { 2024: 0, 2025: 0, 2026: 0 };
      let surface = { 2024: 0, 2025: 0, 2026: 0 };
      let other = { 2024: 0, 2025: 0, 2026: 0 };

      finalData.forEach((item) => {
        const period = item.reporting_period;
        let fiscalYear = "";

        if (period.includes("to")) {
          const match = period.match(/Apr-(\d{4}) to Mar-(\d{4})/);
          if (match) fiscalYear = match[2];
        } else {
          const match = period.match(/([A-Za-z]{3}|\d{2})-(\d{4})/);
          if (match) {
            const monthStr = match[1];
            const year = +match[2];
            const monthMap: Record<string, number> = {
              Jan: 0,
              Feb: 1,
              Mar: 2,
              Apr: 3,
              May: 4,
              Jun: 5,
              Jul: 6,
              Aug: 7,
              Sep: 8,
              Oct: 9,
              Nov: 10,
              Dec: 11,
            };
            const monthNum = isNaN(+monthStr)
              ? monthMap[monthStr]
              : +monthStr - 1;
            fiscalYear =
              monthNum >= 3 ? (year + 1).toString() : year.toString();
          }
        }

        if (!fiscalYear) return;

        if (item.title == "Surface water") {
          const val = parseFloat(item.computedValue);
          surface[fiscalYear] += isNaN(val) ? 0 : val;
        } else if (item.title == "Ground Water") {
          ground[fiscalYear] += +item.computedValue || 0;
        } else {
          other[fiscalYear] += +item.computedValue || 0;
        }
      });

      data["Ground Water"] = Object.values(ground).map(
        (item) => +item.toFixed(2)
      );
      data["Surface Water"] = Object.values(surface).map(
        (item) => +item.toFixed(2)
      );
      data["Third Party Water"] = Object.values(other).map(
        (item) => +item.toFixed(2)
      );

      setScopeData(data);
    })();
  }, [selectedRegion, location]);

  const handleDrilldown = (source: string, yearIndex: number) => {
    if (!enableDrilldown) return;
    const years = ["2024", "2025", "2026"];
    setDrilldown({ isActive: true, source, year: years[yearIndex] });
  };

  const handleBack = () => setDrilldown(null);

  // if (enableDrilldown && drilldown?.isActive) {
  //   return (
  //     <EnvironmentDrilldownChart
  //       metricType="withdrawal"
  //       region={location.country}
  //       year={drilldown.year}
  //       category={drilldown.source}
  //       onBack={handleBack}
  //       monthlyDrilldownData={waterMonthlyDrilldownData.monthlyDrilldownData}
  //     />
  //   );
  // }

  const series = sources.map((source, idx) => ({
    type: "column" as const,
    name: source,
    data: data[source],
    color: Highcharts.getOptions().colors?.[idx % 10] || undefined,
  }));

  const options: Highcharts.Options = {
    chart: {
      type: "column",
      backgroundColor: "#fff",
      style: { fontFamily: "inherit" },
    },
    title: { text: undefined },
    xAxis: {
      categories: ["FY-2024", "FY-2025", "FY-2026"],
      title: { text: "Fiscal Year" },
      labels: { style: { fontSize: "14px", color: "#334155" } },
    },
    yAxis: {
      min: 0,
      title: {
        text: "Water Withdrawal (Mega Liters, ML)",
        style: { fontSize: "14px", color: "#334155" },
      },
      labels: {
        formatter: function () {
          return this.value + "";
        },
        style: { fontSize: "13px", color: "#64748b" },
      },
      gridLineColor: "#e5e7eb",
    },
    legend: {
      enabled: true,
      layout: "vertical",
      align: "right",
      verticalAlign: "middle",
    },
    tooltip: {
      shared: true,
      headerFormat: "<b>{point.key}</b><br/>",
      pointFormat:
        '<span style="color:{series.color}">{series.name}</span>: <b>{point.y} ML</b><br/>',
    },
    plotOptions: {
      column: {
        grouping: true,
        borderRadius: 4,
        pointPadding: 0.1,
        groupPadding: 0.15,
        dataLabels: {
          enabled: false,
        },
        point: {
          events: {
            click: function () {
              if (!enableDrilldown) return;
              handleDrilldown(this.series.name, this.x);
            },
          },
        },
        cursor: enableDrilldown ? "pointer" : "default",
      },
    },
    series: series,
    credits: { enabled: false },
  };

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <EnvironmentLocationFilter value={location} onChange={setLocation} />
      {enableDrilldown && (
        <div className="mb-2 text-sm text-gray-600">
          💡 Click on any bar to view monthly breakdown
        </div>
      )}
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};
