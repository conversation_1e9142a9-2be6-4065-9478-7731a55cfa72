import React, { useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

interface Scope2DistributionByLocationChartProps {
  region?: string;
  enableDrilldown?: boolean;
  useLocationFilter?: boolean;
  data?: any[];
  BUList?: any[];
}

const Scope2DistributionByLocationChart: React.FC<Scope2DistributionByLocationChartProps> = ({
  region = "Global",
  enableDrilldown = false,
  useLocationFilter = false,
  data = [],
  BUList = []
}) => {
  console.log("Scope2 Data:", data);
  console.log("BUList:", BUList);

  // Process real data - filter by level 3 and group by entity/location
  const processedLocationData = React.useMemo(() => {
    if (!data || data.length === 0) {
      console.log("No data provided to Scope2DistributionByLocationChart");
      return [];
    }

    console.log("Total data records:", data.length);
    console.log("Sample data record:", data[0]);

    // Debug: Check what levels and indicators we have
    const levels = [...new Set(data.map(item => item.level))];
    const indicators = [...new Set(data.map(item => item.indicatorId))];
    console.log("Available levels:", levels);
    console.log("Available indicators:", indicators);

    // Filter data by indicatorId 169 (Scope 2) - try different levels
    let filteredData = data.filter(item => item.indicatorId === 169);
    console.log("Indicator 169 data (any level):", filteredData.length, filteredData);

    // If we have data, prefer level 3, but fall back to any level if needed
    if (filteredData.length > 0) {
      const level3Data = filteredData.filter(item => item.level === 3);
      if (level3Data.length > 0) {
        filteredData = level3Data;
        console.log("Using level 3 data:", filteredData.length);
      } else {
        console.log("No level 3 data found, using all levels for indicator 169");
      }
    }

    // Group by entity (Location/Business Unit)
    const groupedData = filteredData.reduce((acc, item) => {
      const entity = item.entity;
      const computedValue = parseFloat(item.computedValue) || 0;
      const emissionFactor = parseFloat(item.emissionFactorValue) || 0;

      if (!acc[entity]) {
        acc[entity] = {
          name: entity,
          value: 0,
          locationId: item.locationId,
          count: 0,
          gridFactor: emissionFactor,
          periods: []
        };
      }

      acc[entity].value += computedValue;
      acc[entity].count += 1;
      acc[entity].periods.push(item.reporting_period);

      return acc;
    }, {});

    // Convert to array and calculate percentages
    const locationArray = Object.values(groupedData);
    const total = locationArray.reduce((sum, item: any) => sum + item.value, 0);

    // Add colors and percentages
    const colors = ["#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#06b6d4", "#ec4899"];

    return locationArray.map((item: any, index) => ({
      ...item,
      percentage: total > 0 ? ((item.value as number) / total) * 100 : 0,
      color: colors[index % colors.length]
    }));
  }, [data]);

  const locationData = processedLocationData;
  const total = locationData.reduce((sum, item) => sum + item.value, 0);

  // Process monthly data by location
  const monthlyData = React.useMemo(() => {
    if (!data || data.length === 0) {
      return { categories: [], series: [] };
    }

    // Get all unique periods and sort them
    const allPeriods = [...new Set(data.map(item => item.reporting_period))].sort();
    const categories = allPeriods.map(period => {
      const [month, year] = period.split('-');
      return month;
    });

    // Create series for each location
    const series = locationData.map(location => {
      const locationDataPoints = data.filter(item =>
        item.entity === location.name &&
        item.indicatorId === 169
      );

      const monthlyValues = categories.map(month => {
        const monthData = locationDataPoints.find(item =>
          item.reporting_period.startsWith(month)
        );
        return monthData ? parseFloat(monthData.computedValue) || 0 : 0;
      });

      return {
        name: location.name,
        data: monthlyValues,
        color: location.color
      };
    });

    return { categories, series };
  }, [data, locationData]);

  const pieChartOptions = {
    chart: {
      type: 'pie',
      backgroundColor: 'transparent',
      height: 400,
    },
    title: {
      text: 'Distribution by Location - Scope 2 Emissions',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    subtitle: {
      text: `Total: ${total.toFixed(2)} tCO₂e | ${locationData.length} Locations`,
      style: {
        fontSize: '14px',
        color: '#6b7280'
      }
    },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        dataLabels: {
          enabled: true,
          format: '<b>{point.name}</b>: {point.percentage:.1f}%',
          style: {
            fontSize: '12px'
          }
        },
        showInLegend: true
      }
    },
    series: [{
      name: 'Emissions',
      colorByPoint: true,
      showInLegend: true,
      data: locationData.map(item => ({
        name: item.name,
        y: item.value,
        color: item.color,
        showInLegend: true
      }))
    }],
    tooltip: {
      formatter: function() {
        return `<b>${this.point.name}</b><br/>
                Emissions: <b>${this.y.toFixed(2)} tCO₂e</b><br/>
                Percentage: <b>${this.percentage.toFixed(1)}%</b>`;
      }
    },
    legend: {
      enabled: true,
      align: 'right',
      verticalAlign: 'middle',
      layout: 'vertical',
      itemStyle: {
        fontSize: '12px'
      },
      useHTML: false
    },
    credits: {
      enabled: false
    }
  };

  const stackedColumnOptions = {
    chart: {
      type: 'column',
      backgroundColor: 'transparent',
      height: 400,
    },
    title: {
      text: 'Monthly Distribution by Location',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    xAxis: {
      categories: monthlyData.categories,
      title: {
        text: 'Month'
      }
    },
    yAxis: {
      title: {
        text: 'Emissions (tCO₂e)'
      },
      stackLabels: {
        enabled: true,
        style: {
          fontWeight: 'bold',
          color: '#374151'
        }
      }
    },
    plotOptions: {
      column: {
        stacking: 'normal',
        dataLabels: {
          enabled: false
        }
      }
    },
    series: monthlyData.series,
    tooltip: {
      formatter: function() {
        return `<b>${this.x}</b><br/>
                ${this.series.name}: <b>${this.y.toFixed(1)} tCO₂e</b><br/>
                Total: <b>${this.point.stackTotal.toFixed(1)} tCO₂e</b>`;
      }
    },
    legend: {
      align: 'center',
      verticalAlign: 'bottom',
      layout: 'horizontal'
    },
    credits: {
      enabled: false
    }
  };

  const [viewType, setViewType] = useState<'pie' | 'stacked'>('pie');

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="mb-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Distribution by Location - Scope 2 Emissions</h3>
            <p className="text-sm text-gray-600">Breakdown of indirect emissions by facility location</p>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setViewType('pie')}
              className={`px-3 py-1 text-sm rounded ${
                viewType === 'pie' 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Distribution
            </button>
            <button
              onClick={() => setViewType('stacked')}
              className={`px-3 py-1 text-sm rounded ${
                viewType === 'stacked' 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Monthly Trend
            </button>
          </div>
        </div>
      </div>
      
      <HighchartsReact
        key={`scope2-chart-${viewType}`}
        highcharts={Highcharts}
        options={viewType === 'pie' ? pieChartOptions : stackedColumnOptions}
      />
      
      {/* Summary Cards */}
    

      {/* No Data Message */}
      {locationData.length === 0 && (
        <div className="mt-4 text-center py-8">
          <div className="text-gray-500">
            No Scope 2 data available for location distribution.
            <br />
        
          </div>
        </div>
      )}
    </div>
  );
};

export default Scope2DistributionByLocationChart;
