import React, { useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

interface Scope2DistributionByBUChartProps {
  region?: string;
  enableDrilldown?: boolean;
  useLocationFilter?: boolean;
}

const Scope2DistributionByBUChart: React.FC<Scope2DistributionByBUChartProps> = ({ 
  region = "Global", 
  enableDrilldown = false, 
  useLocationFilter = false 
}) => {
  // Sample data for Distribution by BU - Scope 2 Emissions
  const buData = [
    { name: "Manufacturing", value: 1567.8, percentage: 45.4, color: "#3b82f6", consumption: 1912.0 },
    { name: "Operations", value: 1034.5, percentage: 29.9, color: "#10b981", consumption: 1262.8 },
    { name: "R&D", value: 567.3, percentage: 16.4, color: "#f59e0b", consumption: 692.5 },
    { name: "Administration", value: 287.2, percentage: 8.3, color: "#ef4444", consumption: 350.6 }
  ];

  const total = buData.reduce((sum, item) => sum + item.value, 0);
  const totalConsumption = buData.reduce((sum, item) => sum + item.consumption, 0);

  // Monthly data by BU
  const monthlyData = {
    categories: ["Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Jan", "Feb"],
    series: [
      {
        name: "Manufacturing",
        data: [142.5, 148.7, 155.4, 162.1, 158.9, 149.3, 164.5, 156.9, 159.6, 157.3, 159.7],
        color: "#3b82f6"
      },
      {
        name: "Operations",
        data: [94.0, 98.2, 102.6, 107.1, 104.7, 96.3, 107.5, 101.9, 104.6, 102.3, 104.7],
        color: "#10b981"
      },
      {
        name: "R&D",
        data: [51.6, 54.2, 56.6, 59.1, 57.7, 53.3, 59.5, 56.9, 57.6, 56.3, 57.7],
        color: "#f59e0b"
      },
      {
        name: "Administration",
        data: [26.1, 27.2, 28.6, 29.1, 28.7, 26.3, 29.5, 27.9, 28.6, 27.3, 28.7],
        color: "#ef4444"
      }
    ]
  };

  const pieChartOptions = {
    chart: {
      type: 'pie',
      backgroundColor: 'transparent',
      height: 400,
    },
    title: {
      text: 'Distribution by Business Unit - Scope 2 Emissions',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    subtitle: {
      text: `Total: ${total.toFixed(1)} tCO₂e | Electricity: ${totalConsumption.toFixed(1)} MWh`,
      style: {
        fontSize: '14px',
        color: '#6b7280'
      }
    },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        dataLabels: {
          enabled: true,
          format: '<b>{point.name}</b>: {point.percentage:.1f}%',
          style: {
            fontSize: '12px'
          }
        },
        showInLegend: true
      }
    },
    series: [{
      name: 'Emissions',
      colorByPoint: true,
      data: buData.map(item => ({
        name: item.name,
        y: item.value,
        color: item.color
      }))
    }],
    tooltip: {
      formatter: function() {
        const bu = buData.find(unit => unit.name === this.point.name);
        const intensity = bu ? (bu.value / bu.consumption).toFixed(3) : 'N/A';
        return `<b>${this.point.name}</b><br/>
                Emissions: <b>${this.y.toFixed(1)} tCO₂e</b><br/>
                Percentage: <b>${this.percentage.toFixed(1)}%</b><br/>
                Electricity: <b>${bu?.consumption.toFixed(1)} MWh</b><br/>
                Intensity: <b>${intensity} tCO₂/MWh</b>`;
      }
    },
    legend: {
      align: 'right',
      verticalAlign: 'middle',
      layout: 'vertical',
      itemStyle: {
        fontSize: '12px'
      }
    },
    credits: {
      enabled: false
    }
  };

  const combinedChartOptions = {
    chart: {
      backgroundColor: 'transparent',
      height: 400,
    },
    title: {
      text: 'Monthly Emissions and Electricity Consumption by BU',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    xAxis: {
      categories: monthlyData.categories,
      title: {
        text: 'Month'
      }
    },
    yAxis: [{
      title: {
        text: 'Emissions (tCO₂e)',
        style: {
          color: '#3b82f6'
        }
      },
      stackLabels: {
        enabled: true,
        style: {
          fontWeight: 'bold',
          color: '#374151'
        }
      }
    }, {
      title: {
        text: 'Electricity Consumption (MWh)',
        style: {
          color: '#10b981'
        }
      },
      opposite: true
    }],
    plotOptions: {
      column: {
        stacking: 'normal',
        dataLabels: {
          enabled: false
        }
      }
    },
    series: [
      ...monthlyData.series.map(series => ({
        ...series,
        type: 'column',
        yAxis: 0
      })),
      {
        name: 'Total Electricity Consumption',
        type: 'line',
        yAxis: 1,
        data: monthlyData.categories.map((_, index) => 
          monthlyData.series.reduce((sum, series) => sum + (series.data[index] * 1.22), 0) // Convert emissions to consumption
        ),
        color: '#10b981',
        marker: {
          enabled: true,
          radius: 4
        },
        dataLabels: {
          enabled: true,
          format: '{y:.0f}',
          style: {
            color: '#10b981'
          }
        }
      }
    ],
    tooltip: {
      shared: true,
      formatter: function() {
        let tooltip = `<b>${this.x}</b><br/>`;
        this.points.forEach(point => {
          if (point.series.type === 'column') {
            tooltip += `${point.series.name}: <b>${point.y.toFixed(1)} tCO₂e</b><br/>`;
          } else {
            tooltip += `${point.series.name}: <b>${point.y.toFixed(0)} MWh</b><br/>`;
          }
        });
        return tooltip;
      }
    },
    legend: {
      align: 'center',
      verticalAlign: 'bottom',
      layout: 'horizontal'
    },
    credits: {
      enabled: false
    }
  };

  const [viewType, setViewType] = useState<'pie' | 'combined'>('pie');

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="mb-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Distribution by Business Unit - Scope 2 Emissions</h3>
            <p className="text-sm text-gray-600">Breakdown of indirect emissions by business unit</p>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setViewType('pie')}
              className={`px-3 py-1 text-sm rounded ${
                viewType === 'pie' 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Distribution
            </button>
            <button
              onClick={() => setViewType('combined')}
              className={`px-3 py-1 text-sm rounded ${
                viewType === 'combined' 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Monthly Trend
            </button>
          </div>
        </div>
      </div>
      
      <HighchartsReact
        highcharts={Highcharts}
        options={viewType === 'pie' ? pieChartOptions : combinedChartOptions}
      />
      
      <div className="mt-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {buData.map((item, index) => (
            <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <div 
                  className="w-4 h-4 rounded-full mr-2" 
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-sm font-medium text-gray-900">{item.name}</span>
              </div>
              <div className="text-lg font-bold text-gray-900">{item.value.toFixed(1)}</div>
              <div className="text-sm text-gray-500">{item.percentage.toFixed(1)}%</div>
              <div className="text-xs text-gray-400 mt-1">
                {item.consumption.toFixed(1)} MWh
              </div>
              <div className="text-xs text-gray-400">
                {(item.value / item.consumption).toFixed(3)} tCO₂/MWh
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Scope2DistributionByBUChart;
