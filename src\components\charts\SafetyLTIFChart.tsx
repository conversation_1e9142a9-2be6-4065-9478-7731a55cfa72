import React, { useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

interface SafetyLTIFChartProps {
  region: string;
}

const regionOptions = ["India", "Indonesia", "United Kingdom", "Global"];
const categories = ["Employees", "Contractors", "Workers"];

// Sample data: region -> category -> [FY-2024, FY-2025, FY-2026]
const regionData: Record<string, Record<string, number[]>> = {
  India: {
    Employees: [0.8, 0.7, 0.6],
    Contractors: [1.2, 1.1, 1.0],
    Workers: [1.5, 1.4, 1.3],
  },
  Indonesia: {
    Employees: [0.7, 0.6, 0.5],
    Contractors: [1.0, 0.9, 0.8],
    Workers: [1.3, 1.2, 1.1],
  },
  "United Kingdom": {
    Employees: [0.4, 0.4, 0.3],
    Contractors: [0.7, 0.6, 0.5],
    Workers: [0.9, 0.8, 0.7],
  },
  Global: {
    Employees: [2.0, 1.9, 1.8],
    Contractors: [3.0, 2.8, 2.6],
    Workers: [3.5, 3.3, 3.1],
  },
};

export const SafetyLTIFChart: React.FC<SafetyLTIFChartProps> = ({ region }) => {
  const [selectedRegion, setSelectedRegion] = useState(region);
  const data = regionData[selectedRegion] || regionData["Global"];

  const series = categories.map((cat, idx) => ({
    type: "column" as const,
    name: cat,
    data: data[cat],
    color: Highcharts.getOptions().colors?.[idx % 10] || undefined,
  }));

  const options: Highcharts.Options = {
    chart: {
      type: "column",
      backgroundColor: "#fff",
      style: { fontFamily: 'inherit' },
    },
    title: { text: undefined },
    xAxis: {
      categories: ["FY-2024", "FY-2025", "FY-2026"],
      title: { text: "Fiscal Year" },
      labels: { style: { fontSize: "14px", color: "#334155" } },
    },
    yAxis: {
      min: 0,
      title: {
        text: "LTIF (Million Hours)",
        style: { fontSize: "14px", color: "#334155" },
      },
      labels: {
        formatter: function () { return this.value + ""; },
        style: { fontSize: "13px", color: "#64748b" },
      },
      gridLineColor: "#e5e7eb",
    },
    legend: { enabled: true, layout: 'vertical', align: 'right', verticalAlign: 'middle' },
    tooltip: {
      shared: true,
      headerFormat: '<b>{point.key}</b><br/>',
      pointFormat: '<span style=\"color:{series.color}\">{series.name}</span>: <b>{point.y} Million Hours</b><br/>'
    },
    plotOptions: {
      column: {
        grouping: true,
        borderRadius: 4,
        pointPadding: 0.1,
        groupPadding: 0.15,
        dataLabels: {
          enabled: false,
        },
      },
    },
    series: series,
    credits: { enabled: false },
  };

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="mb-4 flex items-center space-x-3">
        <label htmlFor="safety-ltif-region-select" className="font-medium text-gray-700">Country/Region:</label>
        <select
          id="safety-ltif-region-select"
          value={selectedRegion}
          onChange={e => setSelectedRegion(e.target.value)}
          className="border border-gray-300 rounded px-3 py-1.5 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-400 bg-white shadow-sm"
        >
          {regionOptions.map(opt => (
            <option key={opt} value={opt}>{opt}</option>
          ))}
        </select>
      </div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
}; 