import React, { useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import environmentData from "@/data/environment-emissions-data.json";
import { EnvironmentDrilldownChart } from "./EnvironmentDrilldownChart";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";

const categories = [
  "Purchased goods and services",
  "Capital goods",
  "Fuel and energy related activities",
  "Upstream transportation and distribution",
  "Waste generation in operation",
  "Business travel",
  "Employee commuting",
  "Downstream transportation and distribution",
  "Use of sold products (avg. 15 yrs)",
  "End-of-life treatment of sold products",
  "Investments (Equity investments in subsidiaries >50%)"
];

const countryOptions = ["India", "Indonesia", "United Kingdom", "Global"];
const indiaRegions = ["North", "South", "East", "West"];
const businessUnitsByRegion: Record<string, string[]> = {
  North: ["BU-North-1", "BU-North-2"],
  South: ["BU-South-1", "BU-South-2"],
  East: ["BU-East-1", "BU-East-2"],
  West: ["BU-West-1", "BU-West-2"]
};

// Use environment-specific data
const regionData = environmentData.environmentScope3Data;

interface DrilldownState {
  isActive: boolean;
  category: string;
  country: string;
  region: string;
  businessUnit: string;
  year: string;
}

export const EnvironmentScope3EmissionsByLocationChart: React.FC = () => {
  const [country, setCountry] = useState("Global");
  const [region, setRegion] = useState("");
  const [businessUnit, setBusinessUnit] = useState("");
  const [drilldown, setDrilldown] = useState<DrilldownState>({
    isActive: false,
    category: "",
    country: "",
    region: "",
    businessUnit: "",
    year: ""
  });
  
  let data = regionData[country] || regionData["Global"];
  // Optionally, you can add more granular data selection for region/businessUnit if available

  const handleDrilldown = (category: string, yearIndex: number) => {
    const years = ["2024", "2025", "2026"];
    setDrilldown({
      isActive: true,
      category,
      country,
      region,
      businessUnit,
      year: years[yearIndex]
    });
  };

  const handleBack = () => {
    setDrilldown({
      isActive: false,
      category: "",
      country: "",
      region: "",
      businessUnit: "",
      year: ""
    });
  };

  const series = categories.map((cat, idx) => ({
    type: "column" as const,
    name: cat,
    data: data[cat],
    color: Highcharts.getOptions().colors?.[idx % 10] || undefined,
  }));

  const options: Highcharts.Options = {
    chart: {
      type: "column",
      backgroundColor: "#fff",
      style: { fontFamily: 'inherit' },
    },
    title: { text: undefined },
    xAxis: {
      categories: ["FY-2024", "FY-2025", "FY-2026"],
      title: { text: "Fiscal Year" },
      labels: { style: { fontSize: "14px", color: "#334155" } },
    },
    yAxis: {
      min: 0,
      title: {
        text: "Emissions (Million tCO₂e)",
        style: { fontSize: "14px", color: "#334155" },
      },
      labels: {
        formatter: function () { return this.value + ""; },
        style: { fontSize: "13px", color: "#64748b" },
      },
      gridLineColor: "#e5e7eb",
    },
    legend: { enabled: true, layout: 'vertical', align: 'right', verticalAlign: 'middle' },
    tooltip: {
      shared: true,
      headerFormat: '<b>{point.key}</b><br/>',
      pointFormat: '<span style=\"color:{series.color}\">{series.name}</span>: <b>{point.y} Million tCO₂e</b><br/>'
    },
    plotOptions: {
      column: {
        grouping: true,
        borderRadius: 4,
        pointPadding: 0.1,
        groupPadding: 0.15,
        dataLabels: {
          enabled: false,
        },
        point: {
          events: {
            click: function() {
              const categoryName = this.series.name;
              handleDrilldown(categoryName, this.x);
            }
          }
        },
        cursor: 'pointer'
      },
    },
    series: series,
    credits: { enabled: false },
  };

  if (drilldown.isActive) {
    return (
      <EnvironmentDrilldownChart
        metricType="scope3"
        country={drilldown.country}
        region={drilldown.region}
        businessUnit={drilldown.businessUnit}
        year={drilldown.year}
        category={drilldown.category}
        onBack={handleBack}
      />
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="mb-4 flex flex-wrap items-center gap-4">
        <div>
          <label className="font-medium text-gray-700 block mb-1">Country:</label>
          <Select value={country} onValueChange={value => {
            setCountry(value);
            setRegion("");
            setBusinessUnit("");
          }}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Select Country" />
            </SelectTrigger>
            <SelectContent>
              {countryOptions.map(opt => (
                <SelectItem key={opt} value={opt}>{opt}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        {country === "India" && (
          <div>
            <label className="font-medium text-gray-700 block mb-1">Region:</label>
            <Select value={region} onValueChange={value => {
              setRegion(value);
              setBusinessUnit("");
            }}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Select Region" />
              </SelectTrigger>
              <SelectContent>
                {indiaRegions.map(opt => (
                  <SelectItem key={opt} value={opt}>{opt}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
        {country === "India" && region && (
          <div>
            <label className="font-medium text-gray-700 block mb-1">Business Unit:</label>
            <Select value={businessUnit} onValueChange={setBusinessUnit}>
              <SelectTrigger className="w-52">
                <SelectValue placeholder="Select Business Unit" />
              </SelectTrigger>
              <SelectContent>
                {(businessUnitsByRegion[region] || []).map(opt => (
                  <SelectItem key={opt} value={opt}>{opt}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </div>
      <div className="mb-2 text-sm text-gray-600">
        💡 Click on any bar to view monthly breakdown
      </div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
}; 