
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export interface FilterState {
  country: string;
  businessUnit: string;
  site: string;
  year: string;
}

interface FilterBarProps {
  compact?: boolean;
  showTitle?: boolean;
  filterState?: FilterState;
  onFilterChange?: (filterType: keyof FilterState, value: string) => void;
}

const FilterBar = ({
  compact = false,
  showTitle = true,
  filterState = { country: "all", businessUnit: "all", site: "all", year: "2024" },
  onFilterChange
}: FilterBarProps) => {
  return (
    <div className={`bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200 ${compact ? 'p-4' : 'p-6'}`}>
      {showTitle && (
        <div className="flex flex-col lg:flex-row gap-6 items-start lg:items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">Data Filters</h3>
            <p className="text-sm text-gray-600">Customize your analytics view</p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <span>Last updated:</span>
            <span className="font-medium text-gray-700">2 minutes ago</span>
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          </div>
        </div>
      )}
      <div className="flex flex-wrap items-end gap-3">
        {/* Left side filters - Country, Business Unit, Site */}
        <div className="flex flex-wrap gap-6 flex-1">
          <div className="min-w-[200px]">
            <label className="block text-xs font-medium text-gray-700 mb-1">Country</label>
            <Select value={filterState.country} onValueChange={(value) => onFilterChange?.("country", value)}>
              <SelectTrigger className="h-9 text-sm bg-white/80 border-gray-300 hover:border-blue-400 focus:border-blue-500 transition-colors">
                <SelectValue placeholder="Select Country" />
              </SelectTrigger>
              <SelectContent className="bg-white border border-gray-200 shadow-xl z-50 rounded-lg">
                <SelectItem value="all">All Countries</SelectItem>
                <SelectItem value="us">United States</SelectItem>
                <SelectItem value="uk">United Kingdom</SelectItem>
                <SelectItem value="de">Germany</SelectItem>
                <SelectItem value="in">India</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="min-w-[200px]">
            <label className="block text-xs font-medium text-gray-700 mb-1">Business Unit</label>
            <Select value={filterState.businessUnit} onValueChange={(value) => onFilterChange?.("businessUnit", value)}>
              <SelectTrigger className="h-9 text-sm bg-white/80 border-gray-300 hover:border-blue-400 focus:border-blue-500 transition-colors">
                <SelectValue placeholder="Select Business Unit" />
              </SelectTrigger>
              <SelectContent className="bg-white border border-gray-200 shadow-xl z-50 rounded-lg">
                <SelectItem value="all">All Business Units</SelectItem>
                <SelectItem value="manufacturing">Manufacturing</SelectItem>
                <SelectItem value="operations">Operations</SelectItem>
                <SelectItem value="logistics">Logistics</SelectItem>
                <SelectItem value="corporate">Corporate</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="min-w-[200px]">
            <label className="block text-xs font-medium text-gray-700 mb-1">Site</label>
            <Select value={filterState.site} onValueChange={(value) => onFilterChange?.("site", value)}>
              <SelectTrigger className="h-9 text-sm bg-white/80 border-gray-300 hover:border-blue-400 focus:border-blue-500 transition-colors">
                <SelectValue placeholder="Select Site" />
              </SelectTrigger>
              <SelectContent className="bg-white border border-gray-200 shadow-xl z-50 rounded-lg">
                <SelectItem value="all">All Sites</SelectItem>
                <SelectItem value="hq">Headquarters</SelectItem>
                <SelectItem value="plant1">Manufacturing Plant 1</SelectItem>
                <SelectItem value="plant2">Manufacturing Plant 2</SelectItem>
                <SelectItem value="warehouse">Distribution Center</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Right side filter - Year */}
        <div className="min-w-[200px]">
          <label className="block text-xs font-medium text-gray-700 mb-1">Year</label>
          <Select value={filterState.year} onValueChange={(value) => onFilterChange?.("year", value)}>
            <SelectTrigger className="h-8 text-sm bg-white/80 border-gray-300 hover:border-blue-400 focus:border-blue-500 transition-colors">
              <SelectValue placeholder="Select Year" />
            </SelectTrigger>
            <SelectContent className="bg-white border border-gray-200 shadow-xl z-50 rounded-lg">
              <SelectItem value="2024">2024</SelectItem>
              <SelectItem value="2023">2023</SelectItem>
              <SelectItem value="2022">2022</SelectItem>
              <SelectItem value="2021">2021</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};

export default FilterBar;
