import React from 'react';
import { 
  ContextMenu, 
  ContextMenuContent, 
  ContextMenuItem, 
  ContextMenuSeparator, 
  ContextMenuTrigger 
} from '@/components/ui/context-menu';
import {
  Download,
  Copy,
  Maximize2,
  Settings,
  RefreshCw,
  Share2,
  <PERSON>er,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  MoreVertical
} from 'lucide-react';

interface ChartContextMenuProps {
  children: React.ReactNode;
  chartTitle?: string;
  onDownload?: () => void;
  onCopy?: () => void;
  onFullscreen?: () => void;
  onSettings?: () => void;
  onRefresh?: () => void;
  onShare?: () => void;
  onPrint?: () => void;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onReset?: () => void;
}

export const ChartContextMenu: React.FC<ChartContextMenuProps> = ({
  children,
  chartTitle = "Chart",
  onDownload,
  onCopy,
  onFullscreen,
  onSettings,
  onRefresh,
  onShare,
  onPrint,
  onZoomIn,
  onZoomOut,
  onReset
}) => {
  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else {
      // Default download functionality
      console.log(`Downloading ${chartTitle}...`);
      // You can implement default download logic here
    }
  };

  const handleCopy = () => {
    if (onCopy) {
      onCopy();
    } else {
      // Default copy functionality
      console.log(`Copying ${chartTitle} to clipboard...`);
      // You can implement default copy logic here
    }
  };

  const handleFullscreen = () => {
    if (onFullscreen) {
      onFullscreen();
    } else {
      // Default fullscreen functionality
      console.log(`Opening ${chartTitle} in fullscreen...`);
      // You can implement default fullscreen logic here
    }
  };

  const handleSettings = () => {
    if (onSettings) {
      onSettings();
    } else {
      // Default settings functionality
      console.log(`Opening ${chartTitle} settings...`);
      // You can implement default settings logic here
    }
  };

  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
    } else {
      // Default refresh functionality
      console.log(`Refreshing ${chartTitle}...`);
      // You can implement default refresh logic here
    }
  };

  const handleShare = () => {
    if (onShare) {
      onShare();
    } else {
      // Default share functionality
      console.log(`Sharing ${chartTitle}...`);
      // You can implement default share logic here
    }
  };

  const handlePrint = () => {
    if (onPrint) {
      onPrint();
    } else {
      // Default print functionality
      console.log(`Printing ${chartTitle}...`);
      window.print();
    }
  };

  const handleZoomIn = () => {
    if (onZoomIn) {
      onZoomIn();
    } else {
      console.log(`Zooming in ${chartTitle}...`);
    }
  };

  const handleZoomOut = () => {
    if (onZoomOut) {
      onZoomOut();
    } else {
      console.log(`Zooming out ${chartTitle}...`);
    }
  };

  const handleReset = () => {
    if (onReset) {
      onReset();
    } else {
      console.log(`Resetting ${chartTitle}...`);
    }
  };

  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>
        <div className="relative w-full h-full cursor-pointer group">
          {children}
          {/* Context menu indicator */}
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-background/80 backdrop-blur-sm rounded-md p-1 shadow-sm border">
            <MoreVertical className="h-4 w-4 text-muted-foreground" />
          </div>
        </div>
      </ContextMenuTrigger>
      <ContextMenuContent className="w-56">
        <ContextMenuItem onClick={handleDownload}>
          <Download className="mr-2 h-4 w-4" />
          Download Chart
        </ContextMenuItem>
        <ContextMenuItem onClick={handleCopy}>
          <Copy className="mr-2 h-4 w-4" />
          Copy to Clipboard
        </ContextMenuItem>
        <ContextMenuItem onClick={handleShare}>
          <Share2 className="mr-2 h-4 w-4" />
          Share Chart
        </ContextMenuItem>
        <ContextMenuSeparator />
        <ContextMenuItem onClick={handleZoomIn}>
          <ZoomIn className="mr-2 h-4 w-4" />
          Zoom In
        </ContextMenuItem>
        <ContextMenuItem onClick={handleZoomOut}>
          <ZoomOut className="mr-2 h-4 w-4" />
          Zoom Out
        </ContextMenuItem>
        <ContextMenuItem onClick={handleReset}>
          <RotateCcw className="mr-2 h-4 w-4" />
          Reset View
        </ContextMenuItem>
        <ContextMenuSeparator />
        <ContextMenuItem onClick={handleFullscreen}>
          <Maximize2 className="mr-2 h-4 w-4" />
          View Fullscreen
        </ContextMenuItem>
        <ContextMenuItem onClick={handlePrint}>
          <Printer className="mr-2 h-4 w-4" />
          Print Chart
        </ContextMenuItem>
        <ContextMenuSeparator />
        <ContextMenuItem onClick={handleRefresh}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh Data
        </ContextMenuItem>
        <ContextMenuItem onClick={handleSettings}>
          <Settings className="mr-2 h-4 w-4" />
          Chart Settings
        </ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  );
};

export default ChartContextMenu;
