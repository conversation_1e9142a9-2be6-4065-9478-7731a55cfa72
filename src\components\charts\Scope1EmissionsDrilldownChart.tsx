import React, { useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";

const countryOptions = ["India", "Indonesia", "United Kingdom", "Global"];
const indiaRegions = ["North", "South", "East", "West"];
const businessUnitsByRegion: Record<string, string[]> = {
  North: ["BU-North-1", "BU-North-2"],
  South: ["BU-South-1", "BU-South-2"],
  East: ["BU-East-1", "BU-East-2"],
  West: ["BU-West-1", "BU-West-2"]
};

// Use the same data as ScopeEmissionsBarChart for country-level
const regionData: Record<string, { scope1: number[] }> = {
  India: { scope1: [0.7, 0.6, 0.5] },
  Indonesia: { scope1: [0.9, 0.8, 0.7] },
  "United Kingdom": { scope1: [0.3, 0.3, 0.2] },
  Global: { scope1: [1.0, 0.9, 0.8] },
};

// Sample data for India drill-down (region + business unit)
const indiaDrilldownData: Record<string, number[]> = {
  "North|BU-North-1": [0.25, 0.22, 0.20],
  "North|BU-North-2": [0.18, 0.17, 0.16],
  "South|BU-South-1": [0.30, 0.28, 0.27],
  "South|BU-South-2": [0.22, 0.21, 0.20],
  "East|BU-East-1": [0.15, 0.14, 0.13],
  "East|BU-East-2": [0.12, 0.11, 0.10],
  "West|BU-West-1": [0.20, 0.19, 0.18],
  "West|BU-West-2": [0.17, 0.16, 0.15],
};

const Scope1EmissionsDrilldownChart: React.FC = () => {
  const [country, setCountry] = useState("Global");
  const [region, setRegion] = useState("");
  const [businessUnit, setBusinessUnit] = useState("");

  let chartData: number[] = [0, 0, 0];
  if (country === "India" && region && businessUnit) {
    chartData = indiaDrilldownData[`${region}|${businessUnit}`] || [0, 0, 0];
  } else if (country === "India") {
    chartData = regionData["India"].scope1;
  } else if (regionData[country]) {
    chartData = regionData[country].scope1;
  }

  const options: Highcharts.Options = {
    chart: {
      type: "column",
      backgroundColor: "#fff",
      style: { fontFamily: 'inherit' }
    },
    title: { text: undefined },
    xAxis: {
      categories: ["FY-2024", "FY-2025", "FY-2026"],
      title: { text: "Fiscal Year" },
      labels: { style: { fontSize: "14px", color: "#334155" } }
    },
    yAxis: {
      min: 0,
      title: {
        text: "Emissions (Million tCO₂e)",
        style: { fontSize: "14px", color: "#334155" }
      },
      labels: {
        formatter: function () { return this.value + ""; },
        style: { fontSize: "13px", color: "#64748b" }
      },
      gridLineColor: "#e5e7eb"
    },
    legend: { enabled: false },
    tooltip: {
      shared: true,
      headerFormat: '<b>{point.key}</b><br/>',
      pointFormat: '<span style="color:{series.color}">{series.name}</span>: <b>{point.y} Million tCO₂e</b><br/>'
    },
    plotOptions: {
      column: {
        grouping: false,
        borderRadius: 6,
        pointPadding: 0.1,
        groupPadding: 0.15,
        dataLabels: {
          enabled: true,
          format: "{y}",
          style: { fontWeight: "bold", color: "#334155" }
        }
      }
    },
    series: [
      {
        type: "column",
        name: "Scope 1 Emissions",
        data: chartData,
        color: "#3b82f6"
      }
    ],
    credits: { enabled: false }
  };

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="mb-4 flex flex-wrap items-center gap-4">
        <div>
          <label className="font-medium text-gray-700 block mb-1">Country/Region:</label>
          <Select value={country} onValueChange={value => {
            setCountry(value);
            setRegion("");
            setBusinessUnit("");
          }}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Select Country/Region" />
            </SelectTrigger>
            <SelectContent>
              {countryOptions.map(opt => (
                <SelectItem key={opt} value={opt}>{opt}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        {country === "India" && (
          <div>
            <label className="font-medium text-gray-700 block mb-1">Region:</label>
            <Select value={region} onValueChange={value => {
              setRegion(value);
              setBusinessUnit("");
            }}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Select Region" />
              </SelectTrigger>
              <SelectContent>
                {indiaRegions.map(opt => (
                  <SelectItem key={opt} value={opt}>{opt}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
        {country === "India" && region && (
          <div>
            <label className="font-medium text-gray-700 block mb-1">Business Unit:</label>
            <Select value={businessUnit} onValueChange={setBusinessUnit}>
              <SelectTrigger className="w-52">
                <SelectValue placeholder="Select Business Unit" />
              </SelectTrigger>
              <SelectContent>
                {(businessUnitsByRegion[region] || []).map(opt => (
                  <SelectItem key={opt} value={opt}>{opt}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};

export default Scope1EmissionsDrilldownChart; 