import React from "react";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";

const countryOptions = ["India", "Indonesia", "United Kingdom", "Global"];
const indiaRegions = ["North", "South", "East", "West"];
const businessUnitsByRegion: Record<string, string[]> = {
  North: ["BU-North-1", "BU-North-2"],
  South: ["BU-South-1", "BU-South-2"],
  East: ["BU-East-1", "BU-East-2"],
  West: ["BU-West-1", "BU-West-2"],
};

export interface EnvironmentLocation {
  country: string;
  region: string;
  businessUnit: string;
}

interface EnvironmentLocationFilterProps {
  value: EnvironmentLocation;
  onChange: (value: EnvironmentLocation) => void;
  className?: string;
}

export const EnvironmentLocationFilter: React.FC<EnvironmentLocationFilterProps> = ({ value, onChange, className }) => {
  const { country, region, businessUnit } = value;

  return (
    <div className={`flex flex-wrap items-center gap-4 ${className || ""}`}>
      <div>
        <label className="font-medium text-gray-700 block mb-1">Country:</label>
        <Select value={country} onValueChange={val => onChange({ country: val, region: "", businessUnit: "" })}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Select Country" />
          </SelectTrigger>
          <SelectContent>
            {countryOptions.map(opt => (
              <SelectItem key={opt} value={opt}>{opt}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      {country === "India" && (
        <div>
          <label className="font-medium text-gray-700 block mb-1">Region:</label>
          <Select value={region} onValueChange={val => onChange({ country, region: val, businessUnit: "" })}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Select Region" />
            </SelectTrigger>
            <SelectContent>
              {indiaRegions.map(opt => (
                <SelectItem key={opt} value={opt}>{opt}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}
      {country === "India" && region && (
        <div>
          <label className="font-medium text-gray-700 block mb-1">Business Unit:</label>
          <Select value={businessUnit} onValueChange={val => onChange({ country, region, businessUnit: val })}>
            <SelectTrigger className="w-52">
              <SelectValue placeholder="Select Business Unit" />
            </SelectTrigger>
            <SelectContent>
              {(businessUnitsByRegion[region] || []).map(opt => (
                <SelectItem key={opt} value={opt}>{opt}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}
    </div>
  );
}; 