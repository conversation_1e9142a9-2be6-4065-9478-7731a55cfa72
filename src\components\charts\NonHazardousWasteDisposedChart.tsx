import React, { useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { EnvironmentDrilldownChart } from "./EnvironmentDrilldownChart";
import nonHazardousWasteMonthlyDrilldownData from "@/data/nonhazardous-waste-monthly-drilldown-data.json";
import { EnvironmentLocationFilter, EnvironmentLocation } from "./EnvironmentLocationFilter";

interface NonHazardousWasteDisposedChartProps {
  region: string;
  enableDrilldown?: boolean;
  useLocationFilter?: boolean; // true for Environment tab, false for Overview tab
}

const regionOptions = ["India", "Indonesia", "United Kingdom", "Global"];
const categories = [
  "Plastic Waste",
  "Construction and Demolition Waste",
  "Other Non-Hazardous Waste"
];

// Sample data: region -> category -> [FY-2024, FY-2025, FY-2026]
const regionData: Record<string, Record<string, number[]>> = {
  India: {
    "Plastic Waste": [0.30, 0.28, 0.26],
    "Construction and Demolition Waste": [0.50, 0.48, 0.45],
    "Other Non-Hazardous Waste": [1.70, 1.54, 1.39],
  },
  Indonesia: {
    "Plastic Waste": [0.25, 0.23, 0.22],
    "Construction and Demolition Waste": [0.40, 0.38, 0.36],
    "Other Non-Hazardous Waste": [1.35, 1.29, 1.22],
  },
  "United Kingdom": {
    "Plastic Waste": [0.10, 0.09, 0.08],
    "Construction and Demolition Waste": [0.20, 0.19, 0.18],
    "Other Non-Hazardous Waste": [0.90, 0.82, 0.74],
  },
  Global: {
    "Plastic Waste": [0.65, 0.60, 0.55],
    "Construction and Demolition Waste": [1.10, 1.05, 1.00],
    "Other Non-Hazardous Waste": [4.25, 4.05, 3.85],
  },
};

export const NonHazardousWasteDisposedChart: React.FC<NonHazardousWasteDisposedChartProps> = ({ region, enableDrilldown = false, useLocationFilter = false }) => {
  const [location, setLocation] = useState<EnvironmentLocation>({ country: region || "Global", region: "", businessUnit: "" });
  const [selectedRegion, setSelectedRegion] = useState(region || "Global");
  const [drilldown, setDrilldown] = useState<{ isActive: boolean; category: string; year: string } | null>(null);
  // Data selection logic: prefer businessUnit > region > country
  let data = useLocationFilter
    ? (regionData[location.country] || regionData["Global"])
    : (regionData[selectedRegion] || regionData["Global"]);
  // (Extend here if you add region/businessUnit-level data)

  const handleDrilldown = (category: string, yearIndex: number) => {
    if (!enableDrilldown) return;
    const years = ["2024", "2025", "2026"];
    setDrilldown({ isActive: true, category, year: years[yearIndex] });
  };

  const handleBack = () => setDrilldown(null);

  if (enableDrilldown && drilldown?.isActive) {
    return (
      <EnvironmentDrilldownChart
        metricType={drilldown.category}
        region={location.country}
        year={drilldown.year}
        category={drilldown.category}
        onBack={handleBack}
        monthlyDrilldownData={nonHazardousWasteMonthlyDrilldownData.monthlyDrilldownData}
      />
    );
  }

  const series = categories.map((cat, idx) => ({
    type: "column" as const,
    name: cat,
    data: data[cat],
    color: Highcharts.getOptions().colors?.[idx % 10] || undefined,
  }));

  const options: Highcharts.Options = {
    chart: {
      type: "column",
      backgroundColor: "#fff",
      style: { fontFamily: 'inherit' },
    },
    title: { text: undefined },
    xAxis: {
      categories: ["FY-2024", "FY-2025", "FY-2026"],
      title: { text: "Fiscal Year" },
      labels: { style: { fontSize: "14px", color: "#334155" } },
    },
    yAxis: {
      min: 0,
      title: {
        text: "Non-Hazardous Waste Disposed (Million Tons, MT)",
        style: { fontSize: "14px", color: "#334155" },
      },
      labels: {
        formatter: function () { return this.value + ""; },
        style: { fontSize: "13px", color: "#64748b" },
      },
      gridLineColor: "#e5e7eb",
    },
    legend: { enabled: true, layout: 'vertical', align: 'right', verticalAlign: 'middle' },
    tooltip: {
      shared: true,
      headerFormat: '<b>{point.key}</b><br/>',
      pointFormat: '<span style=\"color:{series.color}\">{series.name}</span>: <b>{point.y} MT</b><br/>'
    },
    plotOptions: {
      column: {
        grouping: true,
        borderRadius: 4,
        pointPadding: 0.1,
        groupPadding: 0.15,
        dataLabels: {
          enabled: false,
        },
        point: {
          events: {
            click: function () {
              if (!enableDrilldown) return;
              handleDrilldown(this.series.name, this.x);
            }
          }
        },
        cursor: enableDrilldown ? 'pointer' : 'default',
      },
    },
    series: series,
    credits: { enabled: false },
  };

  return (
    <div className="bg-white rounded-lg shadow p-4">
      {useLocationFilter ? (
        <EnvironmentLocationFilter value={location} onChange={setLocation} />
      ) : (
        <div className="mb-4 flex items-center space-x-3">
          <label htmlFor="non-hazardous-waste-region-select" className="font-medium text-gray-700">Country/Region:</label>
          <select
            id="non-hazardous-waste-region-select"
            value={selectedRegion}
            onChange={e => setSelectedRegion(e.target.value)}
            className="border border-gray-300 rounded px-3 py-1.5 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-400 bg-white shadow-sm"
          >
            {Object.keys(regionData).map(opt => (
              <option key={opt} value={opt}>{opt}</option>
            ))}
          </select>
        </div>
      )}
      {enableDrilldown && (
        <div className="mb-2 text-sm text-gray-600">
          💡 Click on any bar to view monthly breakdown
        </div>
      )}
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
}; 