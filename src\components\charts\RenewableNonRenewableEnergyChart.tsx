import React, { useEffect, useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { EnvironmentDrilldownChart } from "./EnvironmentDrilldownChart";
import energyMonthlyDrilldownData from "@/data/energy-monthly-drilldown-data.json";
import {
  EnvironmentLocationFilter,
  EnvironmentLocation,
} from "./EnvironmentLocationFilter";
import axios from "axios";

interface RenewableNonRenewableEnergyChartProps {
  region: string;
  suppliersData: any[];
  enableDrilldown?: boolean;
  useLocationFilter?: boolean; // true for Environment tab, false for Overview tab
}

const regionOptions = ["India", "Indonesia", "United Kingdom", "Global"];

// Sample data: region -> { renewable: [FY-2024, FY-2025, FY-2026], nonRenewable: [FY-2024, FY-2025, FY-2026] }
const regionData: Record<
  string,
  { renewable: number[]; nonRenewable: number[] }
> = {
  India: {
    renewable: [12000, 13500, 15000],
    nonRenewable: [30000, 29000, 28000],
  },
  Indonesia: {
    renewable: [9000, 9500, 10000],
    nonRenewable: [25000, 24500, 24000],
  },
  "United Kingdom": {
    renewable: [15000, 16000, 17000],
    nonRenewable: [20000, 19500, 19000],
  },
  Global: {
    renewable: [50000, 54000, 58000],
    nonRenewable: [120000, 115000, 110000],
  },
};

export const RenewableNonRenewableEnergyChart: React.FC<
  RenewableNonRenewableEnergyChartProps
> = ({
  region,
  suppliersData,
  enableDrilldown = true,
  useLocationFilter = false,
}) => {
  const [location, setLocation] = useState<EnvironmentLocation>({
    country: region || "Global",
    region: "",
    businessUnit: "",
  });
  const [selectedRegion, setSelectedRegion] = useState(region || "Global");
  const [drilldown, setDrilldown] = useState<{
    isActive: boolean;
    metricType: string;
    year: string;
  }>({
    isActive: false,
    metricType: "",
    year: "",
  });
  const [supplierData, setSupplierData] = useState<any[]>(suppliersData);
  const [locationData, setLocationData] = useState<any[]>([]);
  const [scopeData, setScopeData] = useState<any>({});

  const data = regionData[selectedRegion] || regionData["Global"];

  const fetchLocationData: any = async (location: string) => {
    try {
      const response = await axios.get(
        "https://api.eisqr.com/user-profiles/289/location-ones?filter=%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationTwos%22%2C%22scope%22%3A%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationThrees%22%7D%5D%7D%7D%5D%7D"
      );

      return response.data;
    } catch (err) {
      console.warn("error:", err);
    }
  };

  const extractAllLocationIds = (locationOne: any): number[] => {
    const ids: number[] = [];
    ids.push(locationOne.id);

    locationOne.locationTwos?.forEach((locationTwo: any) => {
      if (locationTwo.id) ids.push(locationTwo.id);

      locationTwo.locationThrees?.forEach((locationThree: any) => {
        if (locationThree.id) ids.push(locationThree.id);
      });
    });

    return ids;
  };

  useEffect(() => {
    (async () => {
      setSelectedRegion("Global");
      const locationData = await fetchLocationData(selectedRegion);
      setLocationData(locationData);

      setSupplierData(suppliersData);
    })();
  }, []);

  useEffect(() => {
    (async () => {
      let locationIndvData =
        selectedRegion === "Global"
          ? locationData
          : locationData.find((item) => item.name === selectedRegion);

      const allLocationIds = extractAllLocationIds(locationIndvData);

      const finalData =
        selectedRegion === "Global"
          ? supplierData
          : supplierData.filter((item) =>
              allLocationIds.includes(item.locationId)
            );

      // Extract category keys from indicatorTitle

      // Initialize finalObj
      const finalObj: Record<string, Record<string, number>> = {};
      finalObj["renewable"] = { 2024: 0, 2025: 0, 2026: 0 };
      finalObj["nonRenewable"] = { 2024: 0, 2025: 0, 2026: 0 };

      // Map fiscal year and accumulate values
      finalData.forEach((item) => {
        const period = item.reporting_period;
        if (!period) return;

        let fiscalYear = "";

        if (period.includes("to")) {
          const match = period.match(/Apr-(\d{4}) to Mar-(\d{4})/);
          if (match) fiscalYear = match[2];
        } else {
          const match = period.match(/([A-Za-z]{3}|\d{2})-(\d{4})/);
          if (match) {
            const monthStr = match[1];
            const year = +match[2];
            const monthMap: Record<string, number> = {
              Jan: 0,
              Feb: 1,
              Mar: 2,
              Apr: 3,
              May: 4,
              Jun: 5,
              Jul: 6,
              Aug: 7,
              Sep: 8,
              Oct: 9,
              Nov: 10,
              Dec: 11,
            };
            const monthNum = isNaN(+monthStr)
              ? monthMap[monthStr]
              : +monthStr - 1;
            fiscalYear =
              monthNum >= 3 ? (year + 1).toString() : year.toString();
          }
        }

        if (!fiscalYear || !["2024", "2025", "2026"].includes(fiscalYear))
          return;

        if (item.title.split(" > ")[1] === "Renewable") {
          const key = "renewable";
          if (finalObj[key]) {
            finalObj[key][fiscalYear] += +item.value || 0;
          }
        } else {
          const key = "nonRenewable";
          if (finalObj[key]) {
            finalObj[key][fiscalYear] += +item.value || 0;
          }
        }
      });

      // Convert to chart-ready structure
      const result: Record<string, number[]> = {};
      for (const [key, val] of Object.entries(finalObj)) {
        result[key] = [val["2024"], val["2025"], val["2026"]].map(
          (v) => +v.toFixed(2)
        );
      }

      setScopeData(result);
    })();
  }, [selectedRegion]);

  const handleDrilldown = (metricType: string, yearIndex: number) => {
    if (!enableDrilldown) return;
    const years = ["2024", "2025", "2026"];
    setDrilldown({
      isActive: true,
      metricType,
      year: years[yearIndex],
    });
  };

  const handleBack = () => {
    setDrilldown({
      isActive: false,
      metricType: "",
      year: "",
    });
  };

  // if (enableDrilldown && drilldown.isActive) {
  //   return (
  //     <EnvironmentDrilldownChart
  //       metricType={drilldown.metricType}
  //       region={location.country}
  //       year={drilldown.year}
  //       onBack={handleBack}
  //       monthlyDrilldownData={energyMonthlyDrilldownData.monthlyDrilldownData}
  //     />
  //   );
  // }

  const options: Highcharts.Options = {
    chart: {
      type: "column",
      backgroundColor: "#fff",
      style: { fontFamily: "inherit" },
    },
    title: { text: undefined },
    xAxis: {
      categories: ["FY-2024", "FY-2025", "FY-2026"],
      title: { text: "Fiscal Year" },
      labels: { style: { fontSize: "14px", color: "#334155" } },
    },
    yAxis: {
      min: 0,
      title: {
        text: "Energy Consumption (GJ)",
        style: { fontSize: "14px", color: "#334155" },
      },
      labels: {
        formatter: function () {
          return this.value + "";
        },
        style: { fontSize: "13px", color: "#64748b" },
      },
      gridLineColor: "#e5e7eb",
    },
    legend: { enabled: true },
    tooltip: {
      shared: true,
      headerFormat: "<b>{point.key}</b><br/>",
      pointFormat:
        '<span style="color:{series.color}">{series.name}</span>: <b>{point.y} GJ</b><br/>',
    },
    plotOptions: {
      column: {
        grouping: true,
        borderRadius: 6,
        pointPadding: 0.1,
        groupPadding: 0.15,
        dataLabels: {
          enabled: true,
          format: "{y}",
          style: { fontWeight: "bold", color: "#334155" },
        },
        point: {
          events: {
            click: function () {
              if (!enableDrilldown) return;
              const metricType = this.series.name
                .toLowerCase()
                .includes("renewable")
                ? this.series.name.toLowerCase().includes("non")
                  ? "nonrenewable"
                  : "renewable"
                : "";
              handleDrilldown(metricType, this.x);
            },
          },
        },
        cursor: enableDrilldown ? "pointer" : "default",
      },
    },
    series: [
      {
        type: "column",
        name: "Renewable Energy Consumption",
        data: scopeData.renewable,
        color: "#10b981",
      },
      {
        type: "column",
        name: "Non-Renewable Energy Consumption",
        data: scopeData.nonRenewable,
        color: "#f59e0b",
      },
    ],
    credits: { enabled: false },
  };

  return (
    <div className="bg-white rounded-lg shadow p-4">
      {useLocationFilter ? (
        <EnvironmentLocationFilter value={location} onChange={setLocation} />
      ) : (
        <div className="mb-4 flex items-center space-x-3">
          <label
            htmlFor="energy-region-select"
            className="font-medium text-gray-700"
          >
            Country/Region:
          </label>
          <select
            id="energy-region-select"
            value={selectedRegion}
            onChange={(e) => setSelectedRegion(e.target.value)}
            className="border border-gray-300 rounded px-3 py-1.5 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-400 bg-white shadow-sm"
          >
            {Object.keys(regionData).map((opt) => (
              <option key={opt} value={opt}>
                {opt}
              </option>
            ))}
          </select>
        </div>
      )}
      {enableDrilldown && (
        <div className="mb-2 text-sm text-gray-600">
          💡 Click on any bar to view monthly breakdown
        </div>
      )}
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};
