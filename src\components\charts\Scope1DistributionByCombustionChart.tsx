import React, { useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

interface Scope1DistributionByCombustionChartProps {
  region?: string;
  enableDrilldown?: boolean;
  useLocationFilter?: boolean;
  data?: any[];
}

const Scope1DistributionByCombustionChart: React.FC<Scope1DistributionByCombustionChartProps> = ({
  region = "Global",
  enableDrilldown = false,
  useLocationFilter = false,
  data = []
}) => {
  console.log("Scope1 Combustion Data:", data);

  // Process real data for Distribution by Type of Combustion
  const combustionData = React.useMemo(() => {
    if (!data || data.length === 0) {
      return [];
    }

    // Calculate Stationary Combustion (indicator 826)
    const stationaryData = data.filter(item => item.indicatorId === 826);
    const stationaryValue = stationaryData.reduce((sum, item) => {
      return sum + (parseFloat(item.computedValue) || 0);
    }, 0);

    // Calculate Mobile Combustion (indicator 287)
    const mobileData = data.filter(item => item.indicatorId === 827);
    const mobileValue = mobileData.reduce((sum, item) => {
      return sum + (parseFloat(item.computedValue) || 0);
    }, 0);

    console.log("Stationary Combustion (826):", stationaryValue, "from", stationaryData.length, "records");
    console.log("Mobile Combustion (827):", mobileValue, "from", mobileData.length, "records");

    // Create combustion types array
    const combustionTypes = [];

    if (stationaryValue > 0) {
      combustionTypes.push({
        name: "Stationary Combustion",
        value: stationaryValue,
        color: "#3b82f6",
        indicatorId: 826,
        recordCount: stationaryData.length
      });
    }

    if (mobileValue > 0) {
      combustionTypes.push({
        name: "Mobile Combustion",
        value: mobileValue,
        color: "#10b981",
        indicatorId: 827,
        recordCount: mobileData.length
      });
    }

    // Calculate percentages
    const total = combustionTypes.reduce((sum, item) => sum + item.value, 0);

    return combustionTypes.map(item => ({
      ...item,
      percentage: total > 0 ? (item.value / total) * 100 : 0
    }));
  }, [data]);

  const total = combustionData.reduce((sum, item) => sum + item.value, 0);

  // Process monthly data by combustion type
  const monthlyData = React.useMemo(() => {
    if (!data || data.length === 0) {
      return { categories: [], series: [] };
    }

    // Get all unique periods and sort them
    const allPeriods = [...new Set(data.map(item => item.reporting_period))].sort();
    const categories = allPeriods.map(period => {
      const [month, year] = period.split('-');
      return month;
    });

    // Create series for each combustion type
    const series = [];

    // Stationary Combustion (826)
    if (combustionData.some(c => c.indicatorId === 826)) {
      const stationaryValues = categories.map(month => {
        const monthData = data.filter(item =>
          item.reporting_period.startsWith(month) &&
          item.indicatorId === 826
        );
        return monthData.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);
      });

      series.push({
        name: "Stationary Combustion",
        data: stationaryValues,
        color: "#3b82f6"
      });
    }

    // Mobile Combustion (827)
    if (combustionData.some(c => c.indicatorId === 827)) {
      const mobileValues = categories.map(month => {
        const monthData = data.filter(item =>
          item.reporting_period.startsWith(month) &&
          item.indicatorId === 827
        );
        return monthData.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);
      });

      series.push({
        name: "Mobile Combustion",
        data: mobileValues,
        color: "#10b981"
      });
    }

    return { categories, series };
  }, [data, combustionData]);

  const pieChartOptions = {
    chart: {
      type: 'pie',
      backgroundColor: 'transparent',
      height: 400,
    },
    title: {
      text: 'Distribution by Type of Combustion',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    subtitle: {
      text: `Total: ${total.toFixed(2)} tCO₂e | ${combustionData.length} Combustion Types`,
      style: {
        fontSize: '14px',
        color: '#6b7280'
      }
    },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        dataLabels: {
          enabled: true,
          format: '<b>{point.name}</b>: {point.percentage:.1f}%',
          style: {
            fontSize: '12px'
          }
        },
        showInLegend: true
      }
    },
    series: [{
      name: 'Emissions',
      colorByPoint: true,
      data: combustionData.map(item => ({
        name: item.name,
        y: item.value,
        color: item.color
      }))
    }],
    tooltip: {
      formatter: function() {
        const combustionType = combustionData.find(c => c.name === this.point.name);
        return `<b>${this.point.name}</b><br/>
                Emissions: <b>${this.y.toFixed(2)} tCO₂e</b><br/>
                Percentage: <b>${this.percentage.toFixed(1)}%</b><br/>`;
      }
    },
    legend: {
      align: 'right',
      verticalAlign: 'middle',
      layout: 'vertical',
      itemStyle: {
        fontSize: '12px'
      }
    },
    credits: {
      enabled: false
    }
  };

  const stackedColumnOptions = {
    chart: {
      type: 'column',
      backgroundColor: 'transparent',
      height: 400,
    },
    title: {
      text: 'Monthly Distribution by Type of Combustion',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    xAxis: {
      categories: monthlyData.categories,
      title: {
        text: 'Month'
      }
    },
    yAxis: {
      title: {
        text: 'Emissions (tCO₂e)'
      },
      stackLabels: {
        enabled: true,
        style: {
          fontWeight: 'bold',
          color: '#374151'
        }
      }
    },
    plotOptions: {
      column: {
        stacking: 'normal',
        dataLabels: {
          enabled: false
        }
      }
    },
    series: monthlyData.series,
    tooltip: {
      formatter: function() {
        return `<b>${this.x}</b><br/>
                ${this.series.name}: <b>${this.y.toFixed(1)} tCO₂e</b><br/>
                Total: <b>${this.point.stackTotal.toFixed(1)} tCO₂e</b>`;
      }
    },
    legend: {
      align: 'center',
      verticalAlign: 'bottom',
      layout: 'horizontal'
    },
    credits: {
      enabled: false
    }
  };

  const [viewType, setViewType] = useState<'pie' | 'stacked'>('pie');

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="mb-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Distribution by Type of Combustion</h3>
            <p className="text-sm text-gray-600">Breakdown of Scope 1 emissions by combustion type</p>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setViewType('pie')}
              className={`px-3 py-1 text-sm rounded ${
                viewType === 'pie' 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Distribution
            </button>
            <button
              onClick={() => setViewType('stacked')}
              className={`px-3 py-1 text-sm rounded ${
                viewType === 'stacked' 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Monthly Trend
            </button>
          </div>
        </div>
      </div>
      
      <HighchartsReact
        key={`combustion-chart-${viewType}`}
        highcharts={Highcharts}
        options={viewType === 'pie' ? pieChartOptions : stackedColumnOptions}
      />
      
    

      {/* No Data Message */}
      {combustionData.length === 0 && (
        <div className="mt-4 text-center py-8">
          <div className="text-gray-500">
            No combustion data available.
            <br />
           
          </div>
        </div>
      )}
    </div>
  );
};

export default Scope1DistributionByCombustionChart;
