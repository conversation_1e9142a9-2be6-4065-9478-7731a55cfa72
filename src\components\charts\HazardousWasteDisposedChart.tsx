import React, { useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { EnvironmentDrilldownChart } from "./EnvironmentDrilldownChart";
import wasteMonthlyDrilldownData from "@/data/waste-monthly-drilldown-data.json";
import { EnvironmentLocationFilter, EnvironmentLocation } from "./EnvironmentLocationFilter";

interface HazardousWasteDisposedChartProps {
  region: string;
  enableDrilldown?: boolean;
  useLocationFilter?: boolean; // true for Environment tab, false for Overview tab
}

const regionOptions = ["India", "Indonesia", "United Kingdom", "Global"];
const categories = [
  "Battery Waste",
  "Waste sent for Coprocessing",
  "Bio-medical Waste Incineration",
  "E-Waste",
  "Waste sent to Landfill",
  "Waste Recovered",
  "Other Hazardous Waste"
];

// Sample data: region -> category -> [FY-2024, FY-2025, FY-2026]
const regionData: Record<string, Record<string, number[]>> = {
  India: {
    "Battery Waste": [0.05, 0.04, 0.03],
    "Waste sent for Coprocessing": [0.10, 0.09, 0.08],
    "Bio-medical Waste Incineration": [0.03, 0.03, 0.02],
    "E-Waste": [0.02, 0.02, 0.01],
    "Waste sent to Landfill": [0.20, 0.18, 0.16],
    "Waste Recovered": [0.08, 0.07, 0.07],
    "Other Hazardous Waste": [0.04, 0.04, 0.03],
  },
  Indonesia: {
    "Battery Waste": [0.04, 0.03, 0.03],
    "Waste sent for Coprocessing": [0.08, 0.08, 0.07],
    "Bio-medical Waste Incineration": [0.02, 0.02, 0.02],
    "E-Waste": [0.01, 0.01, 0.01],
    "Waste sent to Landfill": [0.15, 0.14, 0.13],
    "Waste Recovered": [0.06, 0.06, 0.05],
    "Other Hazardous Waste": [0.03, 0.03, 0.02],
  },
  "United Kingdom": {
    "Battery Waste": [0.02, 0.02, 0.01],
    "Waste sent for Coprocessing": [0.05, 0.05, 0.04],
    "Bio-medical Waste Incineration": [0.01, 0.01, 0.01],
    "E-Waste": [0.01, 0.01, 0.01],
    "Waste sent to Landfill": [0.08, 0.07, 0.07],
    "Waste Recovered": [0.03, 0.03, 0.03],
    "Other Hazardous Waste": [0.02, 0.02, 0.01],
  },
  Global: {
    "Battery Waste": [0.10, 0.09, 0.08],
    "Waste sent for Coprocessing": [0.20, 0.18, 0.16],
    "Bio-medical Waste Incineration": [0.06, 0.06, 0.05],
    "E-Waste": [0.04, 0.04, 0.03],
    "Waste sent to Landfill": [0.40, 0.36, 0.32],
    "Waste Recovered": [0.15, 0.14, 0.13],
    "Other Hazardous Waste": [0.07, 0.07, 0.06],
  },
};

export const HazardousWasteDisposedChart: React.FC<HazardousWasteDisposedChartProps> = ({ region, enableDrilldown = false, useLocationFilter = false }) => {
  const [location, setLocation] = useState<EnvironmentLocation>({ country: region || "Global", region: "", businessUnit: "" });
  const [selectedRegion, setSelectedRegion] = useState(region || "Global");
  const [drilldown, setDrilldown] = useState<{ isActive: boolean; category: string; year: string } | null>(null);
  // Data selection logic: prefer businessUnit > region > country
  let data = useLocationFilter
    ? (regionData[location.country] || regionData["Global"])
    : (regionData[selectedRegion] || regionData["Global"]);
  // (Extend here if you add region/businessUnit-level data)

  const handleDrilldown = (category: string, yearIndex: number) => {
    if (!enableDrilldown) return;
    const years = ["2024", "2025", "2026"];
    setDrilldown({ isActive: true, category, year: years[yearIndex] });
  };

  const handleBack = () => setDrilldown(null);

  if (enableDrilldown && drilldown?.isActive) {
    return (
      <EnvironmentDrilldownChart
        metricType="hazardous"
        region={location.country}
        year={drilldown.year}
        category={drilldown.category}
        onBack={handleBack}
        monthlyDrilldownData={wasteMonthlyDrilldownData.monthlyDrilldownData}
      />
    );
  }

  const series = categories.map((cat, idx) => ({
    type: "column" as const,
    name: cat,
    data: data[cat],
    color: Highcharts.getOptions().colors?.[idx % 10] || undefined,
  }));

  const options: Highcharts.Options = {
    chart: {
      type: "column",
      backgroundColor: "#fff",
      style: { fontFamily: 'inherit' },
    },
    title: { text: undefined },
    xAxis: {
      categories: ["FY-2024", "FY-2025", "FY-2026"],
      title: { text: "Fiscal Year" },
      labels: { style: { fontSize: "14px", color: "#334155" } },
    },
    yAxis: {
      min: 0,
      title: {
        text: "Hazardous Waste Disposed (Million Tons, MT)",
        style: { fontSize: "14px", color: "#334155" },
      },
      labels: {
        formatter: function () { return this.value + ""; },
        style: { fontSize: "13px", color: "#64748b" },
      },
      gridLineColor: "#e5e7eb",
    },
    legend: { enabled: true, layout: 'vertical', align: 'right', verticalAlign: 'middle' },
    tooltip: {
      shared: true,
      headerFormat: '<b>{point.key}</b><br/>',
      pointFormat: '<span style="color:{series.color}">{series.name}</span>: <b>{point.y} MT</b><br/>'
    },
    plotOptions: {
      column: {
        grouping: true,
        borderRadius: 4,
        pointPadding: 0.1,
        groupPadding: 0.15,
        dataLabels: {
          enabled: false,
        },
        point: {
          events: {
            click: function () {
              if (!enableDrilldown) return;
              handleDrilldown(this.series.name, this.x);
            }
          }
        },
        cursor: enableDrilldown ? 'pointer' : 'default',
      },
    },
    series: series,
    credits: { enabled: false },
  };

  return (
    <div className="bg-white rounded-lg shadow p-4">
      {useLocationFilter ? (
        <EnvironmentLocationFilter value={location} onChange={setLocation} />
      ) : (
        <div className="mb-4 flex items-center space-x-3">
          <label htmlFor="hazardous-waste-region-select" className="font-medium text-gray-700">Country/Region:</label>
          <select
            id="hazardous-waste-region-select"
            value={selectedRegion}
            onChange={e => setSelectedRegion(e.target.value)}
            className="border border-gray-300 rounded px-3 py-1.5 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-400 bg-white shadow-sm"
          >
            {Object.keys(regionData).map(opt => (
              <option key={opt} value={opt}>{opt}</option>
            ))}
          </select>
        </div>
      )}
      {enableDrilldown && (
        <div className="mb-2 text-sm text-gray-600">
          💡 Click on any bar to view monthly breakdown
        </div>
      )}
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
}; 