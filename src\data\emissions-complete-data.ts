export const reportingEntities = [
  'India',
  'Ho<PERSON>r',
  'Mysore',
  'Nalagarh',
  'Pune',
  'Testing Track',
  'Chaintanya'
];

export const scope1FuelTypes = [
  'All',
  'HSD',
  'Petrol',
  'LPG',
  'Propane'
];

export const scope2FuelTypes = [
  'All',
  'Electricity',
  'Heating',
  'Cooling'
];

export const scope3Categories = [
  'All',
  'Purchased Goods & Services',
  'Capital Goods',
  'Fuel and Energy',
  'Upstream Transportation',
  'Waste',
  'Business Travel',
  'Employee Commute'
];

export const renewableEnergyTypes = [
  'All',
  'Wind',
  'Solar',
  'Hydro'
];

export const nonRenewableEnergyTypes = [
  'All',
  'Grid',
  'Third-party'
];

export const waterSourceTypes = [
  'All',
  'Ground Water',
  'Surface Water',
  'Third-party Water'
];

export const hazardousWasteTypes = [
  'All',
  'Bio-Medical',
  'Battery'
];

export const nonHazardousWasteTypes = [
  'All',
  'Plastic',
  'Metal',
  'Paper'
];

// Scope 1 emissions data
export const scope1EmissionsData = {
  India: {
    HSD: {
      fy24Total: 1200,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [120, 115, 125, 118, 110, 105, 100, 95, 90, 85, 80, 75],
        fy26: [130, 125, 120, 115, 110, 105, 100, 95, 90, 85, 80, 70]
      }
    },
    Petrol: {
      fy24Total: 500,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [40, 38, 42, 39, 37, 35, 33, 31, 29, 27, 25, 23],
        fy26: [45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 20]
      }
    },
    LPG: {
      fy24Total: 200,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [15, 14, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7],
        fy26: [18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 6]
      }
    },
    Propane: {
      fy24Total: 100,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [8, 7, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0],
        fy26: [10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0, 0]
      }
    }
  },
  Hosur: {
    HSD: {
      fy24Total: 800,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [80, 78, 75, 72, 70, 68, 65, 62, 60, 58, 55, 52],
        fy26: [85, 82, 80, 78, 75, 72, 70, 68, 65, 62, 60, 55]
      }
    },
    Petrol: {
      fy24Total: 300,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14],
        fy26: [28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 16]
      }
    },
    LPG: {
      fy24Total: 80,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [7, 6, 6, 5, 5, 4, 4, 3, 3, 2, 2, 1],
        fy26: [8, 7, 7, 6, 6, 5, 5, 4, 4, 3, 3, 2]
      }
    },
    Propane: {
      fy24Total: 20,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0],
        fy26: [3, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0]
      }
    }
  },
  Mysore: {
    HSD: { fy24Total: 500, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28], fy26: [48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26] } },
    Petrol: { fy24Total: 200, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9], fy26: [19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8] } },
    LPG: { fy24Total: 80, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [8, 7, 7, 6, 6, 5, 5, 4, 4, 3, 3, 2], fy26: [7, 7, 6, 6, 5, 5, 4, 4, 3, 3, 2, 2] } },
    Propane: { fy24Total: 20, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0], fy26: [2, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0] } }
  },
  Nalagarh: {
    HSD: { fy24Total: 400, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18], fy26: [38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16] } },
    Petrol: { fy24Total: 150, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4], fy26: [14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3] } },
    LPG: { fy24Total: 40, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [4, 3, 3, 3, 2, 2, 2, 1, 1, 1, 0, 0], fy26: [3, 3, 3, 2, 2, 2, 1, 1, 1, 0, 0, 0] } },
    Propane: { fy24Total: 10, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], fy26: [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] } }
  },
  Pune: {
    HSD: { fy24Total: 900, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [90, 87, 84, 81, 78, 75, 72, 69, 66, 63, 60, 57], fy26: [84, 81, 78, 75, 72, 69, 66, 63, 60, 57, 54, 51] } },
    Petrol: { fy24Total: 400, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18], fy26: [36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14] } },
    LPG: { fy24Total: 150, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4], fy26: [13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2] } },
    Propane: { fy24Total: 50, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [5, 4, 4, 3, 3, 2, 2, 1, 1, 0, 0, 0], fy26: [4, 3, 3, 2, 2, 1, 1, 0, 0, 0, 0, 0] } }
  },
  'Testing Track': {
    HSD: { fy24Total: 200, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9], fy26: [18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7] } },
    Petrol: { fy24Total: 80, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [8, 7, 6, 5, 4, 3, 2, 1, 0, 0, 0, 0], fy26: [7, 6, 5, 4, 3, 2, 1, 0, 0, 0, 0, 0] } },
    LPG: { fy24Total: 15, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], fy26: [1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] } },
    Propane: { fy24Total: 5, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], fy26: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] } }
  },
  Chaintanya: {
    HSD: { fy24Total: 300, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19], fy26: [28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17] } },
    Petrol: { fy24Total: 120, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], fy26: [11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0] } },
    LPG: { fy24Total: 25, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [2, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0], fy26: [2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0] } },
    Propane: { fy24Total: 5, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], fy26: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] } }
  }
};

// Scope 2 emissions data (electricity, heating, cooling)
export const scope2EmissionsData = {
  India: {
    Electricity: { fy24Total: 800, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [85, 82, 88, 85, 80, 78, 75, 72, 68, 65, 62, 58], fy26: [90, 87, 85, 82, 78, 75, 72, 68, 65, 62, 58, 55] } },
    Heating: { fy24Total: 300, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [25, 22, 20, 18, 15, 12, 10, 15, 20, 25, 28, 30], fy26: [28, 25, 22, 20, 17, 14, 12, 17, 22, 27, 30, 32] } },
    Cooling: { fy24Total: 400, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [45, 48, 50, 52, 48, 45, 42, 38, 35, 32, 30, 28], fy26: [42, 45, 47, 49, 45, 42, 39, 35, 32, 29, 27, 25] } }
  },
  Hosur: {
    Electricity: { fy24Total: 600, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [60, 58, 55, 52, 50, 48, 45, 42, 40, 38, 35, 32], fy26: [65, 62, 60, 58, 55, 52, 50, 48, 45, 42, 40, 37] } },
    Heating: { fy24Total: 200, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [15, 12, 10, 8, 6, 4, 2, 6, 10, 15, 18, 20], fy26: [18, 15, 12, 10, 8, 6, 4, 8, 12, 17, 20, 22] } },
    Cooling: { fy24Total: 250, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [28, 30, 32, 35, 32, 28, 25, 22, 20, 18, 15, 12], fy26: [25, 27, 29, 32, 29, 25, 22, 19, 17, 15, 12, 10] } }
  },
  Mysore: {
    Electricity: { fy24Total: 400, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18], fy26: [38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16] } },
    Heating: { fy24Total: 120, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [8, 6, 4, 2, 1, 0, 0, 2, 4, 8, 10, 12], fy26: [10, 8, 6, 4, 2, 1, 1, 4, 6, 10, 12, 14] } },
    Cooling: { fy24Total: 180, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [18, 20, 22, 25, 22, 18, 15, 12, 10, 8, 6, 4], fy26: [16, 18, 20, 23, 20, 16, 13, 10, 8, 6, 4, 2] } }
  },
  Nalagarh: {
    Electricity: { fy24Total: 300, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8], fy26: [28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6] } },
    Heating: { fy24Total: 80, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [5, 3, 2, 1, 0, 0, 0, 1, 2, 5, 7, 8], fy26: [7, 5, 3, 2, 1, 0, 0, 2, 3, 7, 9, 10] } },
    Cooling: { fy24Total: 100, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [10, 12, 14, 16, 14, 10, 8, 6, 4, 2, 1, 0], fy26: [8, 10, 12, 14, 12, 8, 6, 4, 2, 1, 0, 0] } }
  },
  Pune: {
    Electricity: { fy24Total: 700, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [70, 68, 65, 62, 60, 58, 55, 52, 50, 48, 45, 42], fy26: [65, 62, 60, 58, 55, 52, 50, 48, 45, 42, 40, 37] } },
    Heating: { fy24Total: 250, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [18, 15, 12, 10, 8, 5, 3, 8, 12, 18, 22, 25], fy26: [20, 17, 14, 12, 10, 7, 5, 10, 14, 20, 24, 27] } },
    Cooling: { fy24Total: 320, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [35, 38, 40, 42, 38, 35, 32, 28, 25, 22, 20, 18], fy26: [32, 35, 37, 39, 35, 32, 29, 25, 22, 19, 17, 15] } }
  },
  'Testing Track': {
    Electricity: { fy24Total: 150, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4], fy26: [13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2] } },
    Heating: { fy24Total: 40, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [2, 1, 1, 0, 0, 0, 0, 0, 1, 2, 3, 4], fy26: [3, 2, 1, 1, 0, 0, 0, 1, 1, 3, 4, 5] } },
    Cooling: { fy24Total: 60, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [6, 7, 8, 9, 8, 6, 5, 4, 3, 2, 1, 0], fy26: [5, 6, 7, 8, 7, 5, 4, 3, 2, 1, 0, 0] } }
  },
  Chaintanya: {
    Electricity: { fy24Total: 200, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9], fy26: [18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7] } },
    Heating: { fy24Total: 80, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [5, 4, 3, 2, 1, 0, 0, 2, 3, 5, 7, 8], fy26: [7, 6, 5, 4, 2, 1, 1, 4, 5, 7, 9, 10] } },
    Cooling: { fy24Total: 120, monthly: { categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'], fy25: [12, 14, 16, 18, 16, 12, 10, 8, 6, 4, 2, 1], fy26: [10, 12, 14, 16, 14, 10, 8, 6, 4, 2, 1, 0] } }
  }
};

// Scope 3 emissions data (indirect emissions from value chain)
// Energy mix data for renewable vs non-renewable comparison
export const energyMixData = {
  India: {
    'Renewable Energy': {
      fy24Total: 450,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [35, 38, 42, 45, 48, 52, 55, 58, 62, 65, 68, 72],
        fy26: [42, 45, 48, 52, 55, 58, 62, 65, 68, 72, 75, 78]
      }
    },
    'Non-Renewable Energy': {
      fy24Total: 1550,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [148, 145, 142, 138, 135, 132, 128, 125, 122, 118, 115, 112],
        fy26: [155, 152, 148, 145, 142, 138, 135, 132, 128, 125, 122, 118]
      }
    }
  },
  Hosur: {
    'Renewable Energy': {
      fy24Total: 320,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [25, 27, 30, 32, 35, 38, 40, 42, 45, 48, 50, 52],
        fy26: [30, 32, 35, 38, 40, 42, 45, 48, 50, 52, 55, 58]
      }
    },
    'Non-Renewable Energy': {
      fy24Total: 1130,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [112, 109, 106, 103, 100, 97, 94, 91, 88, 85, 82, 79],
        fy26: [118, 115, 112, 109, 106, 103, 100, 97, 94, 91, 88, 85]
      }
    }
  },
  Mysore: {
    'Renewable Energy': {
      fy24Total: 200,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [15, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36],
        fy26: [18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40]
      }
    },
    'Non-Renewable Energy': {
      fy24Total: 600,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [78, 76, 74, 72, 70, 68, 66, 64, 62, 60, 58, 56],
        fy26: [74, 72, 70, 68, 66, 64, 62, 60, 58, 56, 54, 52]
      }
    }
  },
  Nalagarh: {
    'Renewable Energy': {
      fy24Total: 150,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
        fy26: [14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]
      }
    },
    'Non-Renewable Energy': {
      fy24Total: 440,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40],
        fy26: [58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36]
      }
    }
  },
  Pune: {
    'Renewable Energy': {
      fy24Total: 380,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [28, 30, 32, 35, 38, 40, 42, 45, 48, 50, 52, 55],
        fy26: [32, 35, 38, 40, 42, 45, 48, 50, 52, 55, 58, 60]
      }
    },
    'Non-Renewable Energy': {
      fy24Total: 1270,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [145, 142, 138, 135, 132, 128, 125, 122, 118, 115, 112, 108],
        fy26: [138, 135, 132, 128, 125, 122, 118, 115, 112, 108, 105, 102]
      }
    }
  },
  'Testing Track': {
    'Renewable Energy': {
      fy24Total: 80,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11],
        fy26: [7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12]
      }
    },
    'Non-Renewable Energy': {
      fy24Total: 220,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [35, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14],
        fy26: [32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10]
      }
    }
  },
  Chaintanya: {
    'Renewable Energy': {
      fy24Total: 120,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
        fy26: [11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]
      }
    },
    'Non-Renewable Energy': {
      fy24Total: 325,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [49, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28],
        fy26: [46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24]
      }
    }
  }
};



// Renewable Energy Generation Data (in MWh)
export const renewableEnergyData = {
  India: {
    Wind: {
      fy24Total: 2400,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [180, 195, 210, 225, 240, 255, 270, 285, 300, 315, 330, 345],
        fy26: [200, 215, 230, 245, 260, 275, 290, 305, 320, 335, 350, 365]
      }
    },
    Solar: {
      fy24Total: 1800,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285],
        fy26: [140, 155, 170, 185, 200, 215, 230, 245, 260, 275, 290, 305]
      }
    },
    Hydro: {
      fy24Total: 1200,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [90, 95, 100, 105, 110, 115, 120, 125, 130, 135, 140, 145],
        fy26: [100, 105, 110, 115, 120, 125, 130, 135, 140, 145, 150, 155]
      }
    }
  },
  Hosur: {
    Wind: {
      fy24Total: 1800,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [135, 145, 155, 165, 175, 185, 195, 205, 215, 225, 235, 245],
        fy26: [150, 160, 170, 180, 190, 200, 210, 220, 230, 240, 250, 260]
      }
    },
    Solar: {
      fy24Total: 1350,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [90, 100, 110, 120, 130, 140, 150, 160, 170, 180, 190, 200],
        fy26: [105, 115, 125, 135, 145, 155, 165, 175, 185, 195, 205, 215]
      }
    },
    Hydro: {
      fy24Total: 900,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [68, 72, 76, 80, 84, 88, 92, 96, 100, 104, 108, 112],
        fy26: [75, 79, 83, 87, 91, 95, 99, 103, 107, 111, 115, 119]
      }
    }
  },
  Mysore: {
    Wind: {
      fy24Total: 1200,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [90, 95, 100, 105, 110, 115, 120, 125, 130, 135, 140, 145],
        fy26: [100, 105, 110, 115, 120, 125, 130, 135, 140, 145, 150, 155]
      }
    },
    Solar: {
      fy24Total: 900,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [60, 65, 70, 75, 80, 85, 90, 95, 100, 105, 110, 115],
        fy26: [70, 75, 80, 85, 90, 95, 100, 105, 110, 115, 120, 125]
      }
    },
    Hydro: {
      fy24Total: 600,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 67],
        fy26: [50, 52, 54, 56, 58, 60, 62, 64, 66, 68, 70, 72]
      }
    }
  },
  Nalagarh: {
    Wind: {
      fy24Total: 1000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [75, 78, 81, 84, 87, 90, 93, 96, 99, 102, 105, 108],
        fy26: [83, 86, 89, 92, 95, 98, 101, 104, 107, 110, 113, 116]
      }
    },
    Solar: {
      fy24Total: 750,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [50, 54, 58, 62, 66, 70, 74, 78, 82, 86, 90, 94],
        fy26: [58, 62, 66, 70, 74, 78, 82, 86, 90, 94, 98, 102]
      }
    },
    Hydro: {
      fy24Total: 500,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49],
        fy26: [42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53]
      }
    }
  },
  Pune: {
    Wind: {
      fy24Total: 2200,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [165, 175, 185, 195, 205, 215, 225, 235, 245, 255, 265, 275],
        fy26: [183, 193, 203, 213, 223, 233, 243, 253, 263, 273, 283, 293]
      }
    },
    Solar: {
      fy24Total: 1650,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [110, 120, 130, 140, 150, 160, 170, 180, 190, 200, 210, 220],
        fy26: [128, 138, 148, 158, 168, 178, 188, 198, 208, 218, 228, 238]
      }
    },
    Hydro: {
      fy24Total: 1100,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [83, 86, 89, 92, 95, 98, 101, 104, 107, 110, 113, 116],
        fy26: [92, 95, 98, 101, 104, 107, 110, 113, 116, 119, 122, 125]
      }
    }
  },
  'Testing Track': {
    Wind: {
      fy24Total: 600,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 67],
        fy26: [50, 52, 54, 56, 58, 60, 62, 64, 66, 68, 70, 72]
      }
    },
    Solar: {
      fy24Total: 450,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52],
        fy26: [35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57]
      }
    },
    Hydro: {
      fy24Total: 300,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34],
        fy26: [25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36]
      }
    }
  },
  Chaintanya: {
    Wind: {
      fy24Total: 800,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [60, 63, 66, 69, 72, 75, 78, 81, 84, 87, 90, 93],
        fy26: [67, 70, 73, 76, 79, 82, 85, 88, 91, 94, 97, 100]
      }
    },
    Solar: {
      fy24Total: 600,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62],
        fy26: [46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 66, 68]
      }
    },
    Hydro: {
      fy24Total: 400,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41],
        fy26: [33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]
      }
    }
  }
};

// Non-Renewable Energy Consumption Data (in MWh)
export const nonRenewableEnergyData = {
  India: {
    Grid: {
      fy24Total: 3200,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [280, 275, 270, 265, 260, 255, 250, 245, 240, 235, 230, 225],
        fy26: [270, 265, 260, 255, 250, 245, 240, 235, 230, 225, 220, 215]
      }
    },
    'Third-party': {
      fy24Total: 1800,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [160, 155, 150, 145, 140, 135, 130, 125, 120, 115, 110, 105],
        fy26: [150, 145, 140, 135, 130, 125, 120, 115, 110, 105, 100, 95]
      }
    }
  },
  Hosur: {
    Grid: {
      fy24Total: 2400,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [210, 205, 200, 195, 190, 185, 180, 175, 170, 165, 160, 155],
        fy26: [200, 195, 190, 185, 180, 175, 170, 165, 160, 155, 150, 145]
      }
    },
    'Third-party': {
      fy24Total: 1350,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [120, 115, 110, 105, 100, 95, 90, 85, 80, 75, 70, 65],
        fy26: [110, 105, 100, 95, 90, 85, 80, 75, 70, 65, 60, 55]
      }
    }
  },
  Mysore: {
    Grid: {
      fy24Total: 1600,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [140, 135, 130, 125, 120, 115, 110, 105, 100, 95, 90, 85],
        fy26: [130, 125, 120, 115, 110, 105, 100, 95, 90, 85, 80, 75]
      }
    },
    'Third-party': {
      fy24Total: 900,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [80, 75, 70, 65, 60, 55, 50, 45, 40, 35, 30, 25],
        fy26: [70, 65, 60, 55, 50, 45, 40, 35, 30, 25, 20, 15]
      }
    }
  },
  Nalagarh: {
    Grid: {
      fy24Total: 1400,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [122, 118, 114, 110, 106, 102, 98, 94, 90, 86, 82, 78],
        fy26: [115, 111, 107, 103, 99, 95, 91, 87, 83, 79, 75, 71]
      }
    },
    'Third-party': {
      fy24Total: 750,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32],
        fy26: [58, 55, 52, 49, 46, 43, 40, 37, 34, 31, 28, 25]
      }
    }
  },
  Pune: {
    Grid: {
      fy24Total: 2800,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [245, 240, 235, 230, 225, 220, 215, 210, 205, 200, 195, 190],
        fy26: [235, 230, 225, 220, 215, 210, 205, 200, 195, 190, 185, 180]
      }
    },
    'Third-party': {
      fy24Total: 1650,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [145, 140, 135, 130, 125, 120, 115, 110, 105, 100, 95, 90],
        fy26: [135, 130, 125, 120, 115, 110, 105, 100, 95, 90, 85, 80]
      }
    }
  },
  'Testing Track': {
    Grid: {
      fy24Total: 800,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [70, 68, 66, 64, 62, 60, 58, 56, 54, 52, 50, 48],
        fy26: [65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43]
      }
    },
    'Third-party': {
      fy24Total: 450,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18],
        fy26: [35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13]
      }
    }
  },
  Chaintanya: {
    Grid: {
      fy24Total: 1200,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [105, 102, 99, 96, 93, 90, 87, 84, 81, 78, 75, 72],
        fy26: [98, 95, 92, 89, 86, 83, 80, 77, 74, 71, 68, 65]
      }
    },
    'Third-party': {
      fy24Total: 600,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30],
        fy26: [48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26]
      }
    }
  }
};

// Water Consumption Data (in Liters)
export const waterConsumptionData = {
  India: {
    'Ground Water': {
      fy24Total: 850000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [75000, 78000, 82000, 85000, 88000, 85000, 80000, 75000, 70000, 68000, 65000, 62000],
        fy26: [72000, 75000, 78000, 82000, 85000, 82000, 78000, 73000, 68000, 65000, 62000, 58000]
      }
    },
    'Surface Water': {
      fy24Total: 650000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [58000, 60000, 62000, 65000, 68000, 65000, 60000, 55000, 52000, 50000, 48000, 45000],
        fy26: [55000, 58000, 60000, 62000, 65000, 62000, 58000, 53000, 50000, 48000, 45000, 42000]
      }
    },
    'Third-party Water': {
      fy24Total: 450000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [40000, 42000, 44000, 46000, 48000, 46000, 42000, 38000, 35000, 33000, 30000, 28000],
        fy26: [38000, 40000, 42000, 44000, 46000, 44000, 40000, 36000, 33000, 30000, 28000, 25000]
      }
    }
  },
  Hosur: {
    'Ground Water': {
      fy24Total: 620000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [55000, 57000, 60000, 62000, 64000, 62000, 58000, 54000, 50000, 48000, 45000, 42000],
        fy26: [52000, 55000, 57000, 60000, 62000, 60000, 56000, 52000, 48000, 45000, 42000, 39000]
      }
    },
    'Surface Water': {
      fy24Total: 480000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [42000, 44000, 46000, 48000, 50000, 48000, 44000, 40000, 37000, 35000, 32000, 30000],
        fy26: [40000, 42000, 44000, 46000, 48000, 46000, 42000, 38000, 35000, 32000, 30000, 27000]
      }
    },
    'Third-party Water': {
      fy24Total: 320000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [28000, 30000, 32000, 34000, 36000, 34000, 30000, 26000, 24000, 22000, 20000, 18000],
        fy26: [26000, 28000, 30000, 32000, 34000, 32000, 28000, 24000, 22000, 20000, 18000, 16000]
      }
    }
  },
  Mysore: {
    'Ground Water': {
      fy24Total: 420000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [37000, 39000, 41000, 43000, 45000, 43000, 39000, 35000, 32000, 30000, 28000, 26000],
        fy26: [35000, 37000, 39000, 41000, 43000, 41000, 37000, 33000, 30000, 28000, 26000, 24000]
      }
    },
    'Surface Water': {
      fy24Total: 320000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [28000, 30000, 32000, 34000, 36000, 34000, 30000, 26000, 24000, 22000, 20000, 18000],
        fy26: [26000, 28000, 30000, 32000, 34000, 32000, 28000, 24000, 22000, 20000, 18000, 16000]
      }
    },
    'Third-party Water': {
      fy24Total: 220000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [19000, 20000, 21000, 22000, 23000, 22000, 20000, 18000, 16000, 15000, 14000, 13000],
        fy26: [18000, 19000, 20000, 21000, 22000, 21000, 19000, 17000, 15000, 14000, 13000, 12000]
      }
    }
  },
  Nalagarh: {
    'Ground Water': {
      fy24Total: 380000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [33000, 35000, 37000, 39000, 41000, 39000, 35000, 31000, 28000, 26000, 24000, 22000],
        fy26: [31000, 33000, 35000, 37000, 39000, 37000, 33000, 29000, 26000, 24000, 22000, 20000]
      }
    },
    'Surface Water': {
      fy24Total: 280000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [24000, 26000, 28000, 30000, 32000, 30000, 26000, 22000, 20000, 18000, 16000, 14000],
        fy26: [22000, 24000, 26000, 28000, 30000, 28000, 24000, 20000, 18000, 16000, 14000, 12000]
      }
    },
    'Third-party Water': {
      fy24Total: 180000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [16000, 17000, 18000, 19000, 20000, 19000, 17000, 15000, 13000, 12000, 11000, 10000],
        fy26: [15000, 16000, 17000, 18000, 19000, 18000, 16000, 14000, 12000, 11000, 10000, 9000]
      }
    }
  },
  Pune: {
    'Ground Water': {
      fy24Total: 720000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [63000, 66000, 69000, 72000, 75000, 72000, 66000, 60000, 55000, 52000, 48000, 45000],
        fy26: [60000, 63000, 66000, 69000, 72000, 69000, 63000, 57000, 52000, 48000, 45000, 42000]
      }
    },
    'Surface Water': {
      fy24Total: 550000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [48000, 50000, 52000, 55000, 58000, 55000, 50000, 45000, 42000, 40000, 37000, 35000],
        fy26: [45000, 48000, 50000, 52000, 55000, 52000, 48000, 43000, 40000, 37000, 35000, 32000]
      }
    },
    'Third-party Water': {
      fy24Total: 380000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [33000, 35000, 37000, 39000, 41000, 39000, 35000, 31000, 28000, 26000, 24000, 22000],
        fy26: [31000, 33000, 35000, 37000, 39000, 37000, 33000, 29000, 26000, 24000, 22000, 20000]
      }
    }
  },
  'Testing Track': {
    'Ground Water': {
      fy24Total: 180000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [16000, 17000, 18000, 19000, 20000, 19000, 17000, 15000, 13000, 12000, 11000, 10000],
        fy26: [15000, 16000, 17000, 18000, 19000, 18000, 16000, 14000, 12000, 11000, 10000, 9000]
      }
    },
    'Surface Water': {
      fy24Total: 140000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [12000, 13000, 14000, 15000, 16000, 15000, 13000, 11000, 10000, 9000, 8000, 7000],
        fy26: [11000, 12000, 13000, 14000, 15000, 14000, 12000, 10000, 9000, 8000, 7000, 6000]
      }
    },
    'Third-party Water': {
      fy24Total: 90000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [8000, 8500, 9000, 9500, 10000, 9500, 8500, 7500, 6500, 6000, 5500, 5000],
        fy26: [7500, 8000, 8500, 9000, 9500, 9000, 8000, 7000, 6000, 5500, 5000, 4500]
      }
    }
  },
  Chaintanya: {
    'Ground Water': {
      fy24Total: 320000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [28000, 30000, 32000, 34000, 36000, 34000, 30000, 26000, 24000, 22000, 20000, 18000],
        fy26: [26000, 28000, 30000, 32000, 34000, 32000, 28000, 24000, 22000, 20000, 18000, 16000]
      }
    },
    'Surface Water': {
      fy24Total: 240000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [21000, 22000, 23000, 24000, 25000, 24000, 22000, 20000, 18000, 17000, 16000, 15000],
        fy26: [20000, 21000, 22000, 23000, 24000, 23000, 21000, 19000, 17000, 16000, 15000, 14000]
      }
    },
    'Third-party Water': {
      fy24Total: 160000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [14000, 15000, 16000, 17000, 18000, 17000, 15000, 13000, 11000, 10000, 9000, 8000],
        fy26: [13000, 14000, 15000, 16000, 17000, 16000, 14000, 12000, 10000, 9000, 8000, 7000]
      }
    }
  }
};

// Water Discharge Data (in Liters)
export const waterDischargeData = {
  India: {
    'Water Discharge': {
      fy24Total: 680000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [60000, 62000, 65000, 68000, 70000, 68000, 63000, 58000, 54000, 52000, 48000, 45000],
        fy26: [57000, 59000, 62000, 65000, 67000, 65000, 60000, 55000, 51000, 48000, 45000, 42000]
      }
    }
  },
  Hosur: {
    'Water Discharge': {
      fy24Total: 496000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [44000, 46000, 48000, 50000, 52000, 50000, 46000, 42000, 39000, 37000, 34000, 32000],
        fy26: [42000, 44000, 46000, 48000, 50000, 48000, 44000, 40000, 37000, 34000, 32000, 29000]
      }
    }
  },
  Mysore: {
    'Water Discharge': {
      fy24Total: 336000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [30000, 31000, 33000, 35000, 37000, 35000, 31000, 27000, 25000, 23000, 21000, 19000],
        fy26: [28000, 30000, 32000, 34000, 36000, 34000, 30000, 26000, 24000, 22000, 20000, 18000]
      }
    }
  },
  Nalagarh: {
    'Water Discharge': {
      fy24Total: 304000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [27000, 28000, 30000, 32000, 34000, 32000, 28000, 24000, 22000, 20000, 18000, 16000],
        fy26: [25000, 27000, 29000, 31000, 33000, 31000, 27000, 23000, 21000, 19000, 17000, 15000]
      }
    }
  },
  Pune: {
    'Water Discharge': {
      fy24Total: 576000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [51000, 53000, 55000, 58000, 61000, 58000, 53000, 48000, 44000, 42000, 38000, 36000],
        fy26: [48000, 51000, 53000, 56000, 59000, 56000, 51000, 46000, 42000, 39000, 36000, 33000]
      }
    }
  },
  'Testing Track': {
    'Water Discharge': {
      fy24Total: 144000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [13000, 14000, 15000, 16000, 17000, 16000, 14000, 12000, 10000, 9000, 8000, 7000],
        fy26: [12000, 13000, 14000, 15000, 16000, 15000, 13000, 11000, 9000, 8000, 7000, 6000]
      }
    }
  },
  Chaintanya: {
    'Water Discharge': {
      fy24Total: 256000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [23000, 24000, 25000, 27000, 29000, 27000, 24000, 21000, 19000, 17000, 15000, 13000],
        fy26: [21000, 23000, 24000, 26000, 28000, 26000, 23000, 20000, 18000, 16000, 14000, 12000]
      }
    }
  }
};

// Hazardous Waste Generation Data (in Kg)
export const hazardousWasteData = {
  India: {
    'Bio-Medical': {
      fy24Total: 12000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [1100, 1150, 1200, 1250, 1300, 1250, 1150, 1050, 950, 900, 850, 800],
        fy26: [1050, 1100, 1150, 1200, 1250, 1200, 1100, 1000, 900, 850, 800, 750]
      }
    },
    Battery: {
      fy24Total: 8000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [720, 750, 780, 810, 840, 810, 750, 690, 630, 580, 530, 480],
        fy26: [680, 710, 740, 770, 800, 770, 710, 650, 590, 540, 490, 440]
      }
    }
  },
  Hosur: {
    'Bio-Medical': {
      fy24Total: 9000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [820, 860, 900, 940, 980, 940, 860, 780, 700, 650, 600, 550],
        fy26: [780, 820, 860, 900, 940, 900, 820, 740, 660, 610, 560, 510]
      }
    },
    Battery: {
      fy24Total: 6000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [540, 565, 590, 615, 640, 615, 565, 515, 465, 430, 395, 360],
        fy26: [510, 535, 560, 585, 610, 585, 535, 485, 435, 400, 365, 330]
      }
    }
  },
  Mysore: {
    'Bio-Medical': {
      fy24Total: 6000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [550, 575, 600, 625, 650, 625, 575, 525, 475, 440, 405, 370],
        fy26: [520, 545, 570, 595, 620, 595, 545, 495, 445, 410, 375, 340]
      }
    },
    Battery: {
      fy24Total: 4000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [360, 375, 390, 405, 420, 405, 375, 345, 315, 290, 265, 240],
        fy26: [340, 355, 370, 385, 400, 385, 355, 325, 295, 270, 245, 220]
      }
    }
  },
  Nalagarh: {
    'Bio-Medical': {
      fy24Total: 5400,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [490, 515, 540, 565, 590, 565, 515, 465, 415, 385, 355, 325],
        fy26: [465, 490, 515, 540, 565, 540, 490, 440, 390, 360, 330, 300]
      }
    },
    Battery: {
      fy24Total: 3600,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [325, 340, 355, 370, 385, 370, 340, 310, 280, 260, 240, 220],
        fy26: [310, 325, 340, 355, 370, 355, 325, 295, 265, 245, 225, 205]
      }
    }
  },
  Pune: {
    'Bio-Medical': {
      fy24Total: 10800,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [980, 1030, 1080, 1130, 1180, 1130, 1030, 930, 830, 770, 710, 650],
        fy26: [930, 980, 1030, 1080, 1130, 1080, 980, 880, 780, 720, 660, 600]
      }
    },
    Battery: {
      fy24Total: 7200,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [650, 680, 710, 740, 770, 740, 680, 620, 560, 520, 480, 440],
        fy26: [620, 650, 680, 710, 740, 710, 650, 590, 530, 490, 450, 410]
      }
    }
  },
  'Testing Track': {
    'Bio-Medical': {
      fy24Total: 2400,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [220, 230, 240, 250, 260, 250, 230, 210, 190, 180, 170, 160],
        fy26: [210, 220, 230, 240, 250, 240, 220, 200, 180, 170, 160, 150]
      }
    },
    Battery: {
      fy24Total: 1600,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [145, 152, 159, 166, 173, 166, 152, 138, 124, 115, 106, 97],
        fy26: [138, 145, 152, 159, 166, 159, 145, 131, 117, 108, 99, 90]
      }
    }
  },
  Chaintanya: {
    'Bio-Medical': {
      fy24Total: 4200,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [385, 403, 421, 439, 457, 439, 403, 367, 331, 307, 283, 259],
        fy26: [367, 385, 403, 421, 439, 421, 385, 349, 313, 289, 265, 241]
      }
    },
    Battery: {
      fy24Total: 2800,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [253, 265, 277, 289, 301, 289, 265, 241, 217, 201, 185, 169],
        fy26: [241, 253, 265, 277, 289, 277, 253, 229, 205, 189, 173, 157]
      }
    }
  }
};

// Non-Hazardous Waste Generation Data (in Kg)
export const nonHazardousWasteData = {
  India: {
    Plastic: {
      fy24Total: 18000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [1650, 1725, 1800, 1875, 1950, 1875, 1725, 1575, 1425, 1320, 1215, 1110],
        fy26: [1575, 1650, 1725, 1800, 1875, 1800, 1650, 1500, 1350, 1245, 1140, 1035]
      }
    },
    Metal: {
      fy24Total: 15000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [1375, 1438, 1500, 1563, 1625, 1563, 1438, 1313, 1188, 1100, 1013, 925],
        fy26: [1313, 1375, 1438, 1500, 1563, 1500, 1375, 1250, 1125, 1038, 950, 863]
      }
    },
    Paper: {
      fy24Total: 12000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [1100, 1150, 1200, 1250, 1300, 1250, 1150, 1050, 950, 880, 810, 740],
        fy26: [1050, 1100, 1150, 1200, 1250, 1200, 1100, 1000, 900, 830, 760, 690]
      }
    }
  },
  Hosur: {
    Plastic: {
      fy24Total: 13500,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [1238, 1294, 1350, 1406, 1463, 1406, 1294, 1181, 1069, 990, 911, 833],
        fy26: [1181, 1238, 1294, 1350, 1406, 1350, 1238, 1125, 1013, 935, 858, 780]
      }
    },
    Metal: {
      fy24Total: 11250,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [1031, 1078, 1125, 1172, 1219, 1172, 1078, 984, 891, 825, 759, 694],
        fy26: [984, 1031, 1078, 1125, 1172, 1125, 1078, 938, 844, 781, 719, 656]
      }
    },
    Paper: {
      fy24Total: 9000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [825, 863, 900, 938, 975, 938, 863, 788, 713, 660, 608, 555],
        fy26: [788, 825, 863, 900, 938, 900, 825, 750, 675, 623, 570, 518]
      }
    }
  },
  Mysore: {
    Plastic: {
      fy24Total: 9000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [825, 863, 900, 938, 975, 938, 863, 788, 713, 660, 608, 555],
        fy26: [788, 825, 863, 900, 938, 900, 825, 750, 675, 623, 570, 518]
      }
    },
    Metal: {
      fy24Total: 7500,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [688, 719, 750, 781, 813, 781, 719, 656, 594, 550, 506, 463],
        fy26: [656, 688, 719, 750, 781, 750, 688, 625, 563, 519, 475, 431]
      }
    },
    Paper: {
      fy24Total: 6000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [550, 575, 600, 625, 650, 625, 575, 525, 475, 440, 405, 370],
        fy26: [525, 550, 575, 600, 625, 600, 550, 500, 450, 415, 380, 345]
      }
    }
  },
  Nalagarh: {
    Plastic: {
      fy24Total: 8100,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [743, 776, 810, 844, 878, 844, 776, 709, 641, 594, 547, 500],
        fy26: [709, 743, 776, 810, 844, 810, 743, 675, 608, 561, 514, 468]
      }
    },
    Metal: {
      fy24Total: 6750,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [619, 647, 675, 703, 731, 703, 647, 591, 534, 495, 456, 416],
        fy26: [591, 619, 647, 675, 703, 675, 619, 563, 506, 468, 431, 393]
      }
    },
    Paper: {
      fy24Total: 5400,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [495, 518, 540, 563, 585, 563, 518, 473, 428, 396, 365, 333],
        fy26: [473, 495, 518, 540, 563, 540, 495, 450, 405, 374, 344, 313]
      }
    }
  },
  Pune: {
    Plastic: {
      fy24Total: 16200,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [1485, 1553, 1620, 1688, 1755, 1688, 1553, 1418, 1283, 1188, 1093, 998],
        fy26: [1418, 1485, 1553, 1620, 1688, 1620, 1485, 1350, 1215, 1123, 1030, 938]
      }
    },
    Metal: {
      fy24Total: 13500,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [1238, 1294, 1350, 1406, 1463, 1406, 1294, 1181, 1069, 990, 911, 833],
        fy26: [1181, 1238, 1294, 1350, 1406, 1350, 1294, 1125, 1013, 935, 858, 780]
      }
    },
    Paper: {
      fy24Total: 10800,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [990, 1035, 1080, 1125, 1170, 1125, 1035, 945, 855, 792, 729, 666],
        fy26: [945, 990, 1035, 1080, 1125, 1080, 990, 900, 810, 749, 688, 626]
      }
    }
  },
  'Testing Track': {
    Plastic: {
      fy24Total: 3600,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [330, 345, 360, 375, 390, 375, 345, 315, 285, 264, 243, 222],
        fy26: [315, 330, 345, 360, 375, 360, 330, 300, 270, 249, 228, 207]
      }
    },
    Metal: {
      fy24Total: 3000,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [275, 288, 300, 313, 325, 313, 288, 263, 238, 220, 203, 185],
        fy26: [263, 275, 288, 300, 313, 300, 275, 250, 225, 208, 190, 173]
      }
    },
    Paper: {
      fy24Total: 2400,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [220, 230, 240, 250, 260, 250, 230, 210, 190, 176, 162, 148],
        fy26: [210, 220, 230, 240, 250, 240, 220, 200, 180, 166, 152, 138]
      }
    }
  },
  Chaintanya: {
    Plastic: {
      fy24Total: 6300,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [578, 604, 630, 656, 683, 656, 604, 551, 499, 462, 425, 388],
        fy26: [551, 578, 604, 630, 656, 630, 578, 525, 473, 437, 401, 365]
      }
    },
    Metal: {
      fy24Total: 5250,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [481, 503, 525, 547, 569, 547, 503, 459, 416, 385, 354, 323],
        fy26: [459, 481, 503, 525, 547, 525, 481, 438, 394, 364, 335, 305]
      }
    },
    Paper: {
      fy24Total: 4200,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [385, 403, 420, 438, 455, 438, 403, 368, 333, 308, 283, 258],
        fy26: [368, 385, 403, 420, 438, 420, 385, 350, 315, 291, 268, 244]
      }
    }
  }
};

// Hazardous Waste Disposal Data (in Kg)
export const hazardousWasteDisposalData = {
  India: {
    'Bio-Medical': {
      fy24Total: 11400,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [1045, 1093, 1140, 1188, 1235, 1188, 1093, 998, 903, 855, 808, 760],
        fy26: [998, 1045, 1093, 1140, 1188, 1140, 1045, 950, 855, 808, 760, 713]
      }
    },
    Battery: {
      fy24Total: 7600,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [684, 713, 741, 770, 798, 770, 713, 656, 599, 551, 504, 456],
        fy26: [646, 675, 703, 732, 760, 732, 675, 618, 561, 513, 466, 418]
      }
    }
  },
  Hosur: {
    'Bio-Medical': {
      fy24Total: 8550,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [779, 817, 855, 893, 931, 893, 817, 741, 665, 618, 570, 523],
        fy26: [741, 779, 817, 855, 893, 855, 779, 703, 627, 580, 532, 485]
      }
    },
    Battery: {
      fy24Total: 5700,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [513, 537, 561, 585, 608, 585, 537, 489, 442, 409, 375, 342],
        fy26: [485, 508, 532, 556, 580, 556, 508, 461, 414, 380, 347, 314]
      }
    }
  },
  Mysore: {
    'Bio-Medical': {
      fy24Total: 5700,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [523, 546, 570, 594, 618, 594, 546, 499, 451, 418, 385, 352],
        fy26: [494, 518, 542, 565, 589, 565, 518, 470, 423, 390, 356, 323]
      }
    },
    Battery: {
      fy24Total: 3800,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [342, 356, 371, 385, 399, 385, 356, 328, 299, 276, 252, 228],
        fy26: [323, 337, 352, 366, 380, 366, 337, 309, 280, 256, 233, 209]
      }
    }
  },
  Nalagarh: {
    'Bio-Medical': {
      fy24Total: 5130,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [466, 489, 513, 537, 561, 537, 489, 442, 395, 366, 337, 309],
        fy26: [442, 466, 489, 513, 537, 513, 466, 418, 371, 342, 314, 285]
      }
    },
    Battery: {
      fy24Total: 3420,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [309, 323, 337, 352, 366, 352, 323, 295, 266, 247, 228, 209],
        fy26: [295, 309, 323, 337, 352, 337, 309, 280, 252, 233, 214, 195]
      }
    }
  },
  Pune: {
    'Bio-Medical': {
      fy24Total: 10260,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [931, 979, 1026, 1074, 1121, 1074, 979, 884, 789, 732, 675, 618],
        fy26: [884, 931, 979, 1026, 1074, 1026, 931, 836, 741, 684, 627, 570]
      }
    },
    Battery: {
      fy24Total: 6840,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [618, 646, 675, 703, 732, 703, 646, 589, 532, 494, 456, 418],
        fy26: [589, 618, 646, 675, 703, 675, 618, 561, 504, 466, 428, 390]
      }
    }
  },
  'Testing Track': {
    'Bio-Medical': {
      fy24Total: 2280,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [209, 219, 228, 238, 247, 238, 219, 200, 181, 171, 162, 152],
        fy26: [200, 209, 219, 228, 238, 228, 209, 190, 171, 162, 152, 143]
      }
    },
    Battery: {
      fy24Total: 1520,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [138, 144, 151, 158, 164, 158, 144, 131, 118, 109, 101, 92],
        fy26: [131, 138, 144, 151, 158, 151, 138, 125, 111, 103, 94, 86]
      }
    }
  },
  Chaintanya: {
    'Bio-Medical': {
      fy24Total: 3990,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [366, 383, 399, 417, 434, 417, 383, 349, 315, 292, 269, 246],
        fy26: [349, 366, 383, 399, 417, 399, 366, 332, 298, 275, 252, 229]
      }
    },
    Battery: {
      fy24Total: 2660,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [241, 252, 263, 275, 286, 275, 252, 229, 206, 191, 176, 161],
        fy26: [229, 241, 252, 263, 275, 263, 241, 218, 195, 180, 164, 149]
      }
    }
  }
};

// Non-Hazardous Waste Disposal Data (in Kg)
export const nonHazardousWasteDisposalData = {
  India: {
    Plastic: {
      fy24Total: 17100,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [1568, 1639, 1710, 1781, 1853, 1781, 1639, 1496, 1354, 1254, 1154, 1055],
        fy26: [1496, 1568, 1639, 1710, 1781, 1710, 1568, 1425, 1283, 1183, 1083, 984]
      }
    },
    Metal: {
      fy24Total: 14250,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [1306, 1366, 1425, 1484, 1544, 1484, 1366, 1247, 1129, 1045, 961, 879],
        fy26: [1247, 1306, 1366, 1425, 1484, 1425, 1306, 1188, 1069, 986, 903, 820]
      }
    },
    Paper: {
      fy24Total: 11400,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [1045, 1093, 1140, 1188, 1235, 1188, 1093, 998, 903, 836, 770, 703],
        fy26: [998, 1045, 1093, 1140, 1188, 1140, 1045, 950, 855, 789, 722, 656]
      }
    }
  },
  Hosur: {
    Plastic: {
      fy24Total: 12825,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [1176, 1229, 1283, 1336, 1389, 1336, 1229, 1122, 1016, 941, 865, 791],
        fy26: [1122, 1176, 1229, 1283, 1336, 1283, 1176, 1069, 962, 888, 815, 741]
      }
    },
    Metal: {
      fy24Total: 10688,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [979, 1024, 1069, 1114, 1158, 1114, 1024, 935, 846, 784, 722, 659],
        fy26: [935, 979, 1024, 1069, 1114, 1069, 1024, 891, 802, 741, 681, 624]
      }
    },
    Paper: {
      fy24Total: 8550,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [784, 819, 855, 891, 926, 891, 819, 749, 678, 627, 577, 527],
        fy26: [749, 784, 819, 855, 891, 855, 784, 713, 641, 592, 542, 492]
      }
    }
  },
  Mysore: {
    Plastic: {
      fy24Total: 8550,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [784, 819, 855, 891, 926, 891, 819, 749, 678, 627, 577, 527],
        fy26: [749, 784, 819, 855, 891, 855, 784, 713, 641, 592, 542, 492]
      }
    },
    Metal: {
      fy24Total: 7125,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [653, 683, 713, 742, 772, 742, 683, 624, 565, 523, 481, 440],
        fy26: [624, 653, 683, 713, 742, 713, 653, 594, 535, 493, 451, 410]
      }
    },
    Paper: {
      fy24Total: 5700,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [523, 546, 570, 594, 618, 594, 546, 499, 451, 418, 385, 352],
        fy26: [499, 523, 546, 570, 594, 570, 523, 475, 428, 395, 361, 328]
      }
    }
  },
  Nalagarh: {
    Plastic: {
      fy24Total: 7695,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [706, 737, 769, 802, 834, 802, 737, 673, 609, 564, 519, 475],
        fy26: [673, 706, 737, 769, 802, 769, 706, 641, 578, 533, 489, 445]
      }
    },
    Metal: {
      fy24Total: 6413,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [588, 614, 641, 668, 695, 668, 614, 561, 508, 470, 433, 395],
        fy26: [561, 588, 614, 641, 668, 641, 588, 535, 482, 444, 409, 373]
      }
    },
    Paper: {
      fy24Total: 5130,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [470, 492, 513, 535, 556, 535, 492, 449, 407, 376, 346, 316],
        fy26: [449, 470, 492, 513, 535, 513, 470, 428, 385, 355, 327, 298]
      }
    }
  },
  Pune: {
    Plastic: {
      fy24Total: 15390,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Sep-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [1411, 1476, 1539, 1602, 1667, 1602, 1476, 1347, 1220, 1129, 1038, 948],
        fy26: [1347, 1411, 1476, 1539, 1602, 1539, 1411, 1283, 1158, 1067, 979, 891]
      }
    },
    Metal: {
      fy24Total: 12825,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [1176, 1229, 1283, 1336, 1389, 1336, 1229, 1122, 1016, 941, 865, 791],
        fy26: [1122, 1176, 1229, 1283, 1336, 1283, 1229, 1069, 962, 888, 815, 741]
      }
    },
    Paper: {
      fy24Total: 10260,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [941, 984, 1026, 1069, 1112, 1069, 984, 898, 813, 752, 692, 633],
        fy26: [898, 941, 984, 1026, 1069, 1026, 941, 855, 770, 712, 654, 595]
      }
    }
  },
  'Testing Track': {
    Plastic: {
      fy24Total: 3420,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [314, 328, 342, 356, 371, 356, 328, 299, 271, 251, 231, 211],
        fy26: [299, 314, 328, 342, 356, 342, 314, 285, 257, 237, 217, 197]
      }
    },
    Metal: {
      fy24Total: 2850,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [261, 274, 285, 297, 309, 297, 274, 250, 226, 209, 193, 176],
        fy26: [250, 261, 274, 285, 297, 285, 261, 238, 214, 197, 181, 164]
      }
    },
    Paper: {
      fy24Total: 2280,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [209, 219, 228, 238, 247, 238, 219, 200, 181, 167, 154, 141],
        fy26: [200, 209, 219, 228, 238, 228, 209, 190, 171, 158, 144, 131]
      }
    }
  },
  Chaintanya: {
    Plastic: {
      fy24Total: 5985,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [549, 574, 599, 623, 649, 623, 574, 524, 474, 439, 404, 369],
        fy26: [524, 549, 574, 599, 623, 599, 549, 499, 449, 415, 381, 347]
      }
    },
    Metal: {
      fy24Total: 4988,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [457, 478, 499, 520, 541, 520, 478, 436, 395, 366, 336, 307],
        fy26: [436, 457, 478, 499, 520, 499, 478, 416, 374, 346, 318, 290]
      }
    },
    Paper: {
      fy24Total: 3990,
      monthly: {
        categories: ['Apr-25', 'May-25', 'Jun-25', 'Jul-25', 'Aug-25', 'Sep-25', 'Oct-25', 'Nov-25', 'Dec-25', 'Jan-26', 'Feb-26', 'Mar-26'],
        fy25: [366, 383, 399, 416, 432, 416, 383, 349, 316, 293, 269, 246],
        fy26: [349, 366, 383, 399, 416, 399, 383, 333, 299, 277, 255, 232]
      }
    }
  }
};
