import React, { useState, useEffect, useRef } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

// Initialize Highcharts modules
let modulesInitialized = false;

const initializeHighchartsModules = async () => {
  if (!modulesInitialized) {
    try {
      const exportingModule = await import('highcharts/modules/exporting');
      const exportDataModule = await import('highcharts/modules/export-data');

      exportingModule.default(Highcharts);
      exportDataModule.default(Highcharts);

      modulesInitialized = true;
      console.log('Highcharts modules initialized');
    } catch (error) {
      console.error('Failed to initialize Highcharts modules:', error);
    }
  }
};
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ChevronDown, X } from 'lucide-react';
import ChartContextMenu from '@/components/ui/chart-with-context-menu';
import {


  waterSourceTypes,
  hazardousWasteTypes,
  nonHazardousWasteTypes,






  hazardousWasteData,
  nonHazardousWasteData,
  hazardousWasteDisposalData,
  nonHazardousWasteDisposalData
} from '@/data/emissions-complete-data';
// Process waste data with proper categorization based on title patterns
const processWasteDataFromJSON = (data: any[], year: number = 2026, fymonth: number = 4, selectedEntity?: string, locationData?: any[], selectedLocationTwo?: string, selectedLocationThree?: string) => {
  // Calculate fiscal years based on passed year parameter
  const currentFY = year;        // FY26 if year = 2026
  const previousFY = year - 1;   // FY25 if year = 2026
  const twoYearsAgoFY = year - 2; // FY24 if year = 2026

  console.log(`=== WASTE DATA FISCAL YEAR CALCULATION ===`);
  console.log(`Base year: ${year}, FY start month: ${fymonth}`);
  console.log(`Target FYs: ${twoYearsAgoFY} (bar), ${previousFY} (line1), ${currentFY} (line2)`);

  // Helper function to determine fiscal year from date string
  const getFiscalYear = (dateStr: string, fyStartMonth: number = fymonth): number => {
    // Handle month name to number conversion
    const monthMap: { [key: string]: number } = {
      'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
      'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
    };

    const [monthStr, yearStr] = dateStr.split('-');
    const month = monthMap[monthStr] || parseInt(monthStr);
    const year = parseInt(yearStr);

    console.log(`getFiscalYear: ${dateStr} -> month=${month}, year=${year}, fyStart=${fyStartMonth}`);

    if (month >= fyStartMonth) {
      const fy = year + 1;
      console.log(`  ${month} >= ${fyStartMonth}, so FY = ${year} + 1 = ${fy}`);
      return fy;
    } else {
      const fy = year;
      console.log(`  ${month} < ${fyStartMonth}, so FY = ${year}`);
      return fy;
    }
  };

  // Filter for waste indicator data only
  let wasteIndicatorData = data.filter((item: any) =>
    item.title && (item.title.includes('Hazardous Waste') || item.title.includes('Non-Hazardous Waste'))
  );

  // Apply location filtering if selectedEntity and locationData are provided
  if (selectedEntity && locationData && locationData.length > 0) {
    const entityData = locationData.find((item: any) => item.name === selectedEntity);
    if (entityData) {
      const relevantLocationIds: number[] = [];

      // Always include the main entity ID
      relevantLocationIds.push(entityData.id);

      // Add location IDs based on selection (same logic as other charts)
      if (entityData.locationTwos) {
        if (selectedLocationTwo === 'All' && selectedLocationThree === 'All') {
          // Include all location IDs from selected entity
          entityData.locationTwos.forEach((locationTwo: any) => {
            relevantLocationIds.push(locationTwo.id);
            if (locationTwo.locationThrees) {
              locationTwo.locationThrees.forEach((locationThree: any) => {
                relevantLocationIds.push(locationThree.id);
              });
            }
          });
        } else if (selectedLocationTwo === 'All' && selectedLocationThree !== 'All') {
          // Search for the specific Tier 3 location across all Tier 2 locations
          entityData.locationTwos.forEach((locationTwo: any) => {
            if (locationTwo.locationThrees) {
              const foundLocationThree = locationTwo.locationThrees.find((item: any) => item.name === selectedLocationThree);
              if (foundLocationThree) {
                relevantLocationIds.push(foundLocationThree.id);
              }
            }
          });
        } else if (selectedLocationThree === 'All') {
          // Include selected Tier 2 and all its Tier 3 locations
          const selectedLocationTwoData = entityData.locationTwos.find((item: any) => item.name === selectedLocationTwo);
          if (selectedLocationTwoData) {
            relevantLocationIds.push(selectedLocationTwoData.id);
            if (selectedLocationTwoData.locationThrees) {
              selectedLocationTwoData.locationThrees.forEach((locationThree: any) => {
                relevantLocationIds.push(locationThree.id);
              });
            }
          }
        } else {
          // Include only selected Tier 3 location
          const selectedLocationTwoData = entityData.locationTwos.find((item: any) => item.name === selectedLocationTwo);
          if (selectedLocationTwoData && selectedLocationTwoData.locationThrees) {
            const selectedLocationThreeData = selectedLocationTwoData.locationThrees.find((item: any) => item.name === selectedLocationThree);
            if (selectedLocationThreeData) {
              relevantLocationIds.push(selectedLocationThreeData.id);
            }
          }
        }
      }

      // Filter waste data by relevant location IDs
      wasteIndicatorData = wasteIndicatorData.filter((item: any) =>
        relevantLocationIds.includes(item.locationId)
      );


    }
  }

  if (!wasteIndicatorData || wasteIndicatorData.length === 0) {
    return {
      hazardousGeneration: { 'India': { 'All': { fy24Total: 0, monthly: { categories: [], fy25: [], fy26: [] } } } },
      nonHazardousGeneration: { 'India': { 'All': { fy24Total: 0, monthly: { categories: [], fy25: [], fy26: [] } } } },
      hazardousDisposal: { 'India': { 'All': { fy24Total: 0, monthly: { categories: [], fy25: [], fy26: [] } } } },
      nonHazardousDisposal: { 'India': { 'All': { fy24Total: 0, monthly: { categories: [], fy25: [], fy26: [] } } } }
    };
  }

  // Categorize waste data based on title patterns - use precise start-of-title matching
  const hazardousGenerationData = wasteIndicatorData.filter((item: any) =>
    item.title && item.title.startsWith('Hazardous Waste Generation')
  );

  const hazardousDisposalData = wasteIndicatorData.filter((item: any) =>
    item.title && item.title.startsWith('Hazardous Waste Disposal')
  );

  const nonHazardousGenerationData = wasteIndicatorData.filter((item: any) =>
    item.title && item.title.startsWith('Non-Hazardous Waste Generation')
  );

  const nonHazardousDisposalData = wasteIndicatorData.filter((item: any) =>
    item.title && item.title.startsWith('Non-Hazardous Waste Disposal')
  );

  // Debug: Log the categorization results
  if (selectedEntity === 'Indonesia') {
    console.log(`=== WASTE CATEGORIZATION DEBUG ===`);
    console.log(`Total waste data: ${wasteIndicatorData.length} items`);
    console.log(`Hazardous Generation: ${hazardousGenerationData.length} items`);
    console.log(`Hazardous Disposal: ${hazardousDisposalData.length} items`);
    console.log(`Non-Hazardous Generation: ${nonHazardousGenerationData.length} items`);
    console.log(`Non-Hazardous Disposal: ${nonHazardousDisposalData.length} items`);

    // Show sample titles to verify categorization
    console.log('Sample Hazardous Generation titles:', hazardousGenerationData.slice(0, 2).map(item => item.title));
    console.log('Sample Non-Hazardous Generation titles:', nonHazardousGenerationData.slice(0, 2).map(item => item.title));
  }

  // Process each waste category separately
  const hazardousGenerationProcessed = processWasteByType(hazardousGenerationData, currentFY, previousFY, twoYearsAgoFY, fymonth, getFiscalYear, "Hazardous Generation");
  const hazardousDisposalProcessed = processWasteByType(hazardousDisposalData, currentFY, previousFY, twoYearsAgoFY, fymonth, getFiscalYear, "Hazardous Disposal");
  const nonHazardousGenerationProcessed = processWasteByType(nonHazardousGenerationData, currentFY, previousFY, twoYearsAgoFY, fymonth, getFiscalYear, "Non-Hazardous Generation");
  const nonHazardousDisposalProcessed = processWasteByType(nonHazardousDisposalData, currentFY, previousFY, twoYearsAgoFY, fymonth, getFiscalYear, "Non-Hazardous Disposal");

  // Use the selected entity name instead of hardcoding 'India'
  const entityKey = selectedEntity || 'India';



  return {
    hazardousGeneration: { [entityKey]: { 'All': hazardousGenerationProcessed } },
    nonHazardousGeneration: { [entityKey]: { 'All': nonHazardousGenerationProcessed } },
    hazardousDisposal: { [entityKey]: { 'All': hazardousDisposalProcessed } },
    nonHazardousDisposal: { [entityKey]: { 'All': nonHazardousDisposalProcessed } }
  };
};

// Process waste data by type following the exact pattern of processRenewableEnergyData
const processWasteByType = (
  data: any[],
  currentFY: number,
  previousFY: number,
  twoYearsAgoFY: number,
  fymonth: number,
  getFiscalYear: (dateStr: string, fyStartMonth: number) => number,
  wasteType: string
) => {
  console.log(`=== PROCESSING ${wasteType.toUpperCase()} WASTE ===`);
  console.log(`Data items: ${data.length}`);
  console.log(`Target FYs: FY${twoYearsAgoFY} (bar), FY${previousFY} (line1), FY${currentFY} (line2)`);

  // Debug: Check a few sample items
  data.slice(0, 3).forEach(item => {
    if (item.reporting_period) {
      const fy = getFiscalYear(item.reporting_period, fymonth);
      console.log(`Sample: ${item.reporting_period} -> FY${fy}`);
    }
  });

  // Separate data by fiscal year using dynamic fiscal years (same as renewable energy)
  const fy24Data = data.filter((item: any) => {
    if (!item.reporting_period) return false;

    // Handle both single month format (Apr-2024) and range format (Apr-2023 to Mar-2024)
    if (item.reporting_period.includes(' to ')) {
      // Range format - check if it spans the target FY
      const [startPeriod, endPeriod] = item.reporting_period.split(' to ');
      const startFY = getFiscalYear(startPeriod, fymonth);
      const endFY = getFiscalYear(endPeriod, fymonth);
      return startFY === twoYearsAgoFY || endFY === twoYearsAgoFY;
    } else {
      // Single month format
      const fy = getFiscalYear(item.reporting_period, fymonth);
      return fy === twoYearsAgoFY;
    }
  });

  const fy25Data = data.filter((item: any) => {
    if (!item.reporting_period) return false;

    if (item.reporting_period.includes(' to ')) {
      const [startPeriod, endPeriod] = item.reporting_period.split(' to ');
      const startFY = getFiscalYear(startPeriod, fymonth);
      const endFY = getFiscalYear(endPeriod, fymonth);
      const matches = startFY === previousFY || endFY === previousFY;
      if (matches) console.log(`FY25 range match: ${item.reporting_period} (${startFY}-${endFY})`);
      return matches;
    } else {
      // Single month format
      const fy = getFiscalYear(item.reporting_period, fymonth);
      const matches = fy === previousFY;
      if (matches) console.log(`FY25 single match: ${item.reporting_period} -> FY${fy}`);
      return matches;
    }
  });

  const fy26Data = data.filter((item: any) => {
    if (!item.reporting_period) return false;

    if (item.reporting_period.includes(' to ')) {
      const [startPeriod, endPeriod] = item.reporting_period.split(' to ');
      const startFY = getFiscalYear(startPeriod, fymonth);
      const endFY = getFiscalYear(endPeriod, fymonth);
      return startFY === currentFY || endFY === currentFY;
    } else {
      const fy = getFiscalYear(item.reporting_period, fymonth);
      return fy === currentFY;
    }
  });

  // Calculate FY24 total using 'value' instead of 'computedValue' for waste data
  const fy24Total = fy24Data.reduce((sum, item) => sum + (parseFloat(item.value) || 0), 0);

  console.log(`=== ${wasteType.toUpperCase()} WASTE FY24 DEBUG ===`);
  console.log(`FY24 data items: ${fy24Data.length}`);
  console.log(`FY24 total: ${fy24Total}`);

  // Create monthly arrays for FY25 and FY26 (same as renewable energy)
  const fy25Monthly = Array(12).fill(null);
  const fy26Monthly = Array(12).fill(null);

  // Process FY25 data (previousFY) - using 'value' instead of 'computedValue'
  fy25Data.forEach(item => {
    const value = parseFloat(item.value) || 0;
    console.log(`Processing FY25 item: ${item.reporting_period}, value: ${value}`);

    if (item.reporting_period && !item.reporting_period.includes(' to ')) {
      // Single month format like "Apr-2024"
      const [monthStr, yearStr] = item.reporting_period.split('-');
      const year = parseInt(yearStr);

      // Map month names to numbers
      const monthMap: { [key: string]: number } = {
        'Apr': 4, 'May': 5, 'Jun': 6, 'Jul': 7, 'Aug': 8, 'Sep': 9,
        'Oct': 10, 'Nov': 11, 'Dec': 12, 'Jan': 1, 'Feb': 2, 'Mar': 3
      };

      const monthNum = monthMap[monthStr];
      console.log(`Month: ${monthStr} -> ${monthNum}, Year: ${year}, FY25 range: ${previousFY-1}-${previousFY}`);

      if (monthNum) {
        // FY25 spans from Apr (previousFY-1) to Mar (previousFY)
        if (year === (previousFY - 1) && monthNum >= fymonth) {
          // Apr-Dec of previous calendar year (e.g., Apr-2024 to Dec-2024 for FY25)
          const monthIndex = monthNum - fymonth;
          console.log(`FY25 Apr-Dec: ${monthStr}-${year} -> index ${monthIndex}`);
          if (fy25Monthly[monthIndex] === null) {
            fy25Monthly[monthIndex] = 0;
          }
          fy25Monthly[monthIndex] += value;
        } else if (year === previousFY && monthNum < fymonth) {
          // Jan-Mar of current fiscal year (e.g., Jan-2025 to Mar-2025 for FY25)
          const monthIndex = monthNum + (12 - fymonth);
          console.log(`FY25 Jan-Mar: ${monthStr}-${year} -> index ${monthIndex}`);
          if (fy25Monthly[monthIndex] === null) {
            fy25Monthly[monthIndex] = 0;
          }
          fy25Monthly[monthIndex] += value;
        }
      }
    }
  });

  // Process FY26 data (currentFY) - using 'value' instead of 'computedValue'
  fy26Data.forEach(item => {
    const value = parseFloat(item.value) || 0;
    console.log(`Processing FY26 item: ${item.reporting_period}, value: ${value}`);

    if (item.reporting_period && !item.reporting_period.includes(' to ')) {
      // Single month format like "Apr-2025"
      const [monthStr, yearStr] = item.reporting_period.split('-');
      const year = parseInt(yearStr);

      // Map month names to numbers
      const monthMap: { [key: string]: number } = {
        'Apr': 4, 'May': 5, 'Jun': 6, 'Jul': 7, 'Aug': 8, 'Sep': 9,
        'Oct': 10, 'Nov': 11, 'Dec': 12, 'Jan': 1, 'Feb': 2, 'Mar': 3
      };

      const monthNum = monthMap[monthStr];
      console.log(`Month: ${monthStr} -> ${monthNum}, Year: ${year}, FY26 range: ${currentFY-1}-${currentFY}`);

      if (monthNum) {
        // FY26 spans from Apr (currentFY-1) to Mar (currentFY)
        if (year === (currentFY - 1) && monthNum >= fymonth) {
          // Apr-Dec of previous calendar year (e.g., Apr-2025 to Dec-2025 for FY26)
          const monthIndex = monthNum - fymonth;
          console.log(`FY26 Apr-Dec: ${monthStr}-${year} -> index ${monthIndex}`);
          if (fy26Monthly[monthIndex] === null) {
            fy26Monthly[monthIndex] = 0;
          }
          fy26Monthly[monthIndex] += value;
        } else if (year === currentFY && monthNum < fymonth) {
          // Jan-Mar of current fiscal year (e.g., Jan-2026 to Mar-2026 for FY26)
          const monthIndex = monthNum + (12 - fymonth);
          console.log(`FY26 Jan-Mar: ${monthStr}-${year} -> index ${monthIndex}`);
          if (fy26Monthly[monthIndex] === null) {
            fy26Monthly[monthIndex] = 0;
          }
          fy26Monthly[monthIndex] += value;
        }
      }
    }
  });

  console.log(`${wasteType} waste FY data counts: FY${twoYearsAgoFY}=${fy24Data.length}, FY${previousFY}=${fy25Data.length}, FY${currentFY}=${fy26Data.length}`);
  console.log(`${wasteType} FY25 monthly data:`, fy25Monthly);
  console.log(`${wasteType} FY26 monthly data:`, fy26Monthly);

  // Return data in the same format as renewable energy
  return {
    fy24Total: fy24Total,
    monthly: {
      categories: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
      fy25: fy25Monthly,
      fy26: fy26Monthly
    }
  };
};

// Multiselect component for fuel types
interface MultiSelectProps {
  options: string[];
  selected: string[];
  onChange: (selected: string[]) => void;
  placeholder?: string;
  className?: string;
}

const MultiSelect: React.FC<MultiSelectProps> = ({
  options,
  selected,
  onChange,
  placeholder = "Select options",
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleToggle = (option: string) => {
    if (option === 'All') {
      // If "All" is selected, clear other selections
      onChange(['All']);
    } else {
      // Remove "All" if it's selected and add/remove the specific option
      const newSelected = selected.filter(item => item !== 'All');
      if (newSelected.includes(option)) {
        const filtered = newSelected.filter(item => item !== option);
        onChange(filtered.length === 0 ? ['All'] : filtered);
      } else {
        onChange([...newSelected, option]);
      }
    }
  };

  const displayText = () => {
    if (selected.includes('All') || selected.length === 0) {
      return 'All';
    }
    if (selected.length === 1) {
      return selected[0];
    }
    return `${selected.length} selected`;
  };

  const removeItem = (option: string, e: React.MouseEvent) => {
    e.stopPropagation();
    const newSelected = selected.filter(item => item !== option);
    onChange(newSelected.length === 0 ? ['All'] : newSelected);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={isOpen}
          className={`w-48 justify-between ${className}`}
        >
          <span className="truncate">{displayText()}</span>
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-48 p-0">
        <div className="max-h-60 overflow-auto">
          {options.map((option) => (
            <div
              key={option}
              className="flex items-center space-x-2 px-3 py-2 hover:bg-gray-100 cursor-pointer"
              onClick={() => handleToggle(option)}
            >
              <Checkbox
                checked={selected.includes(option) || (option !== 'All' && selected.includes('All'))}
                onChange={() => {}} // Handled by parent div click
              />
              <span className="flex-1">{option}</span>
            </div>
          ))}
        </div>
        {!selected.includes('All') && selected.length > 0 && (
          <div className="border-t p-2">
            <div className="flex flex-wrap gap-1">
              {selected.map((item) => (
                <div
                  key={item}
                  className="flex items-center bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded"
                >
                  <span>{item}</span>
                  <X
                    className="ml-1 h-3 w-3 cursor-pointer"
                    onClick={(e) => removeItem(item, e)}
                  />
                </div>
              ))}
            </div>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
};

// Reusable chart component
interface EmissionsChartProps {
  title: string;
  subtitle: string;
  data: Record<string, Record<string, {
    fy24Total: number;
    monthly: {
      categories: string[];
      fy25: number[];
      fy26: number[];
    };
  }>>;
  selectedEntity: string;
  selectedType: string | string[];
  typeOptions: string[];
  yAxisLabel?: string;
  year?: number;
  fymonth?: number;
  unit?: string; // Unit for tooltip display (e.g., 'tCO₂e', 'GJ', 'ML')
  indicationType?: number; // 0 = lower is better (green), 1 = higher is better (green)
}

const EmissionsChart: React.FC<EmissionsChartProps> = ({
  title,
  subtitle,
  data,
  selectedEntity,
  selectedType,
  typeOptions,
  yAxisLabel = 'Emissions (tCO2e)',
  year = 2026,
  fymonth = 4,
  unit = 'tCO₂e', // Default unit for emissions
  indicationType = 0 // Default: 0 = lower is better (green), 1 = higher is better (green)
}) => {
  const chartRef = useRef<HighchartsReact.RefObject>(null);
  const entityData = data[selectedEntity as keyof typeof data];

  // Function to aggregate data based on selected fuel types
  const getProcessedData = () => {
    if (!entityData) {
      return {
        fy24Total: 0,
        monthly: {
          categories: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
          fy25: Array(12).fill(null),
          fy26: Array(12).fill(null)
        }
      };
    }

    if (Array.isArray(selectedType)) {
      // Handle multiselect
      if (selectedType.includes('All') || selectedType.length === 0) {
        // Aggregate all available fuel types
        const allTypes = Object.keys(entityData);
        const aggregated = {
          fy24Total: 0,
          monthly: {
            categories: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
            fy25: Array(12).fill(null),
            fy26: Array(12).fill(null)
          }
        };

        allTypes.forEach(type => {
          const typeData = entityData[type];
          if (typeData) {
            aggregated.fy24Total += typeData.fy24Total;
            typeData.monthly.fy25.forEach((value: number | null, index: number) => {
              if (value !== null) {
                if (aggregated.monthly.fy25[index] === null) {
                  aggregated.monthly.fy25[index] = 0;
                }
                aggregated.monthly.fy25[index] += value;
              }
            });
            typeData.monthly.fy26.forEach((value: number | null, index: number) => {
              if (value !== null) {
                if (aggregated.monthly.fy26[index] === null) {
                  aggregated.monthly.fy26[index] = 0;
                }
                aggregated.monthly.fy26[index] += value;
              }
            });
          }
        });

        return aggregated;
      } else {
        // Aggregate only selected fuel types
        const aggregated = {
          fy24Total: 0,
          monthly: {
            categories: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
            fy25: Array(12).fill(null),
            fy26: Array(12).fill(null)
          }
        };

        selectedType.forEach(type => {
          const typeData = entityData[type];
          if (typeData) {
            aggregated.fy24Total += typeData.fy24Total;
            typeData.monthly.fy25.forEach((value: number | null, index: number) => {
              if (value !== null) {
                if (aggregated.monthly.fy25[index] === null) {
                  aggregated.monthly.fy25[index] = 0;
                }
                aggregated.monthly.fy25[index] += value;
              }
            });
            typeData.monthly.fy26.forEach((value: number | null, index: number) => {
              if (value !== null) {
                if (aggregated.monthly.fy26[index] === null) {
                  aggregated.monthly.fy26[index] = 0;
                }
                aggregated.monthly.fy26[index] += value;
              }
            });
          }
        });

        return aggregated;
      }
    } else {
      // Handle single select (backward compatibility)
      if (selectedType === 'All') {
        // Aggregate all available fuel types from processed data
        const allTypes = Object.keys(entityData);
        const aggregated = {
          fy24Total: 0,
          monthly: {
            categories: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
            fy25: Array(12).fill(null),
            fy26: Array(12).fill(null)
          }
        };

        allTypes.forEach(type => {
          const typeData = entityData[type];
          if (typeData) {
            aggregated.fy24Total += typeData.fy24Total;
            typeData.monthly.fy25.forEach((value: number | null, index: number) => {
              if (value !== null) {
                if (aggregated.monthly.fy25[index] === null) {
                  aggregated.monthly.fy25[index] = 0;
                }
                aggregated.monthly.fy25[index] += value;
              }
            });
            typeData.monthly.fy26.forEach((value: number | null, index: number) => {
              if (value !== null) {
                if (aggregated.monthly.fy26[index] === null) {
                  aggregated.monthly.fy26[index] = 0;
                }
                aggregated.monthly.fy26[index] += value;
              }
            });
          }
        });

        return aggregated;
      } else {
        const typeData = entityData[selectedType];
        if (!typeData) {
          return {
            fy24Total: 0,
            monthly: {
              categories: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
              fy25: Array(12).fill(null),
              fy26: Array(12).fill(null)
            }
          };
        }
        return typeData;
      }
    }
  };

  const currentData = getProcessedData();

  // Function to create FY26 line with dynamic colors based on comparison with FY25
  const createFY26LineData = () => {
    const fy25Data = currentData.monthly.fy25;
    const fy26Data = currentData.monthly.fy26;

    // Create zones for dynamic coloring
    const zones = [];
    let lastValidIndex = -1;

    for (let i = 0; i < fy26Data.length; i++) {
      const fy26Value = fy26Data[i];
      const fy25Value = fy25Data[i];

      if (fy26Value !== null && fy25Value !== null) {
        // Determine improvement based on indicationType
        // indicationType 0: lower is better (default for emissions, waste)
        // indicationType 1: higher is better (for renewable energy, etc.)
        const isImprovement = indicationType === 0
          ? fy26Value <= fy25Value  // Lower is better
          : fy26Value >= fy25Value; // Higher is better

        zones.push({
          value: i + 1.5, // Zone boundary
          color: isImprovement ? '#22c55e' : '#ef4444'
        });
        lastValidIndex = i;
      }
    }

    // Add final zone if we have data
    if (lastValidIndex >= 0) {
      const lastFy26Value = fy26Data[lastValidIndex];
      const lastFy25Value = fy25Data[lastValidIndex];
      const isImprovement = indicationType === 0
        ? lastFy26Value <= lastFy25Value  // Lower is better
        : lastFy26Value >= lastFy25Value; // Higher is better

      zones.push({
        value: fy26Data.length + 1,
        color: isImprovement ? '#22c55e' : '#ef4444'
      });
    }

    return {
      data: [null, ...fy26Data],
      zones: zones.length > 0 ? zones : [{ value: fy26Data.length + 1, color: '#6b7280' }]
    };
  };

  // Create tooltip formatter with dynamic units and proper null handling
  const createTooltipFormatter = (tooltipUnit: string) => {
    return function () {
      let s = `<div style="font-weight: 600; color: #111827; margin-bottom: 8px;">${this.category}</div>`;
      this.points?.forEach(function (point) {
        const color = point.series.color;
        const value = point.y;

        // Always show the series, but handle null values by showing empty
        if (value === null || value === undefined) {
          s += `<div style="margin-bottom: 4px;">
                  <span style="color: ${color}; font-weight: 600;">${point.series.name}:</span>
                  <span style="font-weight: 600; color: #111827;"></span>
                </div>`;
        } else {
          // Format value to 2 decimal places
          const formattedValue = Number(value).toFixed(2);
          s += `<div style="margin-bottom: 4px;">
                  <span style="color: ${color}; font-weight: 600;">${point.series.name}:</span>
                  <span style="font-weight: 600; color: #111827;">${formattedValue} ${tooltipUnit}</span>
                </div>`;
        }
      });
      return s;
    };
  };

  // Check if there's any data to display across all years
  const hasAnyData = () => {
    const fy24HasData = currentData.fy24Total >= 0;
    const fy25HasData = currentData.monthly.fy25.some(val => val !== null && val !== undefined);
    const fy26HasData = currentData.monthly.fy26.some(val => val !== null && val !== undefined);
    return fy24HasData || fy25HasData || fy26HasData;
  };

  // If no data, show "No Data to Show Graph" message
  if (!hasAnyData()) {
    return (
      <div className="bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">{title}</h3>
              <p className="text-sm text-gray-600">{subtitle}</p>
            </div>
          </div>
        </div>
        <div className="p-6 bg-white flex items-center justify-center h-96">
          <div className="text-center">
            <div className="mx-auto mb-4 w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Data to Show Graph</h3>
            <p className="text-gray-500">No data available for the selected criteria across all fiscal years.</p>
          </div>
        </div>
      </div>
    );
  }






  const chartOptions: Highcharts.Options = {
    chart: {
      type: 'column',
      backgroundColor: 'transparent',
      style: {
        fontFamily: 'Inter, system-ui, sans-serif'
      }
    },
    title: {
      text: title,
      style: {
        fontSize: '16px',
        fontWeight: '600',
        color: 'hsl(210 11% 15%)'
      }
    },
    subtitle: {
      text: subtitle,
      style: {
        fontSize: '14px',
        color: 'hsl(210 6% 46%)'
      }
    },
    xAxis: {
      categories: ['FY\'24', ...currentData.monthly.categories],
      crosshair: true,
      labels: {
        style: {
          color: 'hsl(210 11% 15%)'
        }
      }
    },
    yAxis: {
      min: 0, // Always start from 0 unless there are negative values
      startOnTick: true, // Ensure axis starts exactly at min value
      endOnTick: false, // Allow axis to end at a natural value
      title: {
        text: yAxisLabel,
        style: {
          color: 'hsl(210 11% 15%)'
        }
      },
      labels: {
        style: {
          color: 'hsl(210 11% 15%)'
        }
      },
      gridLineColor: 'hsl(210 20% 90%)'
    },
    legend: {
      align: 'right',
      verticalAlign: 'top',
      itemStyle: {
        color: 'hsl(210 11% 15%)'
      }
    },
    plotOptions: {
      column: {
        pointPadding: 0.2,
        borderWidth: 0,
        dataLabels: {
          enabled: false
        }
      },
      line: {
        marker: {
          enabled: true,
          radius: 3,
          lineWidth: 1
        },
        lineWidth: 3
      }
    },
    series: (() => {
      const fy26LineData = createFY26LineData();

      return [{
        name: 'FY\'24 (Total)',
        type: 'column',
        data: [{ x: 0, y: currentData.fy24Total }, ...Array(12).fill(null)],
        color: 'hsl(240 100% 70%)'
      }, {
        name: 'FY\'25',
        type: 'line',
        data: [null, ...currentData.monthly.fy25],
        color: '#9CA3AF', // Grey for previous year
        lineWidth: 3,
        marker: {
          symbol: 'circle',
          radius: 4,
          lineWidth: 1,
          lineColor: '#9CA3AF',
          fillColor: 'white'
        },
        connectNulls: false,
        dataLabels: {
          enabled: true,
          formatter: function() {
            // Show data labels only for first and last month points (excluding total column)
            const dataIndex = this.x;
            const dataArray = [null, ...currentData.monthly.fy25];

            // Find first and last non-null data points (excluding index 0 which is total column)
            let firstDataIndex = -1;
            let lastDataIndex = -1;

            for (let i = 1; i < dataArray.length; i++) { // Start from 1 to skip total column
              if (dataArray[i] !== null) {
                if (firstDataIndex === -1) firstDataIndex = i;
                lastDataIndex = i;
              }
            }

            // Show label only for first and last data points
            if (dataIndex === firstDataIndex || dataIndex === lastDataIndex) {
              return this.y?.toFixed(2);
            }

            return null; // Hide label for other points
          },
          style: {
            color: '#374151',
            fontSize: '11px',
            fontWeight: '600'
          },
          y: -10 // Position above the point
        }
      }, {
        name: 'FY\'26',
        type: 'line',
        data: fy26LineData.data,
        zones: fy26LineData.zones,
        zoneAxis: 'x',
        lineWidth: 3,
        marker: {
          symbol: 'circle',
          radius: 4,
          lineWidth: 1,
          lineColor: 'white',
          fillColor: 'white'
        },
        connectNulls: true // Connect points even when intermediate values are null
      }];
    })(),
    tooltip: {
      shared: true,
      style: { fontSize: "12px", color: "#374151", fontWeight: "400" },
      useHTML: true,
      padding: 12,
      formatter: createTooltipFormatter(unit || 'tCO₂e'),
    },
    credits: {
      enabled: false
    },
    responsive: {
      rules: [{
        condition: {
          maxWidth: 768
        },
        chartOptions: {
          legend: {
            align: 'center',
            verticalAlign: 'bottom',
            layout: 'horizontal'
          },
          title: {
            style: {
              fontSize: '14px'
            }
          }
        }
      }]
    }
  };

  // Context menu handlers
  const handleDownload = () => {
    if (chartRef.current) {
      const chart = chartRef.current.chart as any;
      try {
        // Try to use Highcharts export functionality
        if (chart.exportChart) {
          chart.exportChart({
            type: 'image/png',
            filename: `${title.toLowerCase().replace(/\s+/g, '-')}`
          });
        } else {
          // Fallback: Create a canvas and download
          const svg = chart.container.querySelector('svg');
          if (svg) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            const svgData = new XMLSerializer().serializeToString(svg);
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
            const url = URL.createObjectURL(svgBlob);

            img.onload = () => {
              canvas.width = img.width;
              canvas.height = img.height;
              ctx?.drawImage(img, 0, 0);

              canvas.toBlob((blob) => {
                if (blob) {
                  const link = document.createElement('a');
                  link.download = `${title.toLowerCase().replace(/\s+/g, '-')}.png`;
                  link.href = URL.createObjectURL(blob);
                  link.click();
                  URL.revokeObjectURL(link.href);
                }
              });
              URL.revokeObjectURL(url);
            };
            img.src = url;
          }
        }
      } catch (error) {
        console.error('Download failed:', error);
        alert('Download functionality is not available');
      }
    }
  };

  const handleCopy = async () => {
    if (chartRef.current) {
      const chart = chartRef.current.chart as any;
      try {
        let svgData = '';
        if (chart.getSVG) {
          svgData = chart.getSVG();
        } else {
          // Fallback: get SVG from DOM
          const svg = chart.container.querySelector('svg');
          if (svg) {
            svgData = new XMLSerializer().serializeToString(svg);
          }
        }

        if (svgData) {
          await navigator.clipboard.writeText(svgData);
          console.log('Chart copied to clipboard');
          alert('Chart copied to clipboard!');
        } else {
          throw new Error('Could not extract chart data');
        }
      } catch (err) {
        console.error('Failed to copy chart:', err);
        alert('Copy functionality is not available');
      }
    }
  };

  const handlePrint = () => {
    if (chartRef.current) {
      const chart = chartRef.current.chart as any;
      if (chart.print) {
        chart.print();
      } else {
        window.print();
      }
    }
  };

  const handleZoomIn = () => {
    if (chartRef.current) {
      const chart = chartRef.current.chart as any;
      if (chart.zoom) {
        chart.zoom({
          x: chart.plotLeft,
          y: chart.plotTop,
          width: chart.plotWidth * 0.8,
          height: chart.plotHeight * 0.8
        });
      } else {
        console.log('Zoom functionality not available');
      }
    }
  };

  const handleZoomOut = () => {
    if (chartRef.current) {
      const chart = chartRef.current.chart as any;
      if (chart.zoomOut) {
        chart.zoomOut();
      } else {
        console.log('Zoom out functionality not available');
      }
    }
  };

  const handleReset = () => {
    if (chartRef.current) {
      const chart = chartRef.current.chart as any;
      if (chart.zoomOut) {
        chart.zoomOut();
      } else {
        console.log('Reset functionality not available');
      }
    }
  };

  return (
    <ChartContextMenu
      chartTitle={title}
      onDownload={handleDownload}
      onCopy={handleCopy}
      onPrint={handlePrint}
      onZoomIn={handleZoomIn}
      onZoomOut={handleZoomOut}
      onReset={handleReset}
    >
      <div className="h-96 w-full">
        <HighchartsReact
          ref={chartRef}
          highcharts={Highcharts}
          options={chartOptions}
          containerProps={{
            style: { width: '100%', height: '100%' }
          }}
        />
      </div>
    </ChartContextMenu>
  );
};

// Renewable Energy Pie Chart Component
interface PieChartData {
  name: string;
  y: number;
  color: string;
  actualValue?: number; // Raw value for tooltip display
}

// Reusable Energy Mix Component
const EnergyMixPieChart = ({
  data,
  year,
  fymonth = 4,
  selectedEntity
}: {
  data: any[],
  year: number,
  fymonth?: number,
  selectedEntity: string
}) => {
  const safeParseValue = (item: any): number => {
    const value = item.computedValue;
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return isNaN(numValue) ? 0 : numValue;
  };

  // Helper function to determine fiscal year from date string
  const getFiscalYear = (dateStr: string, fyStartMonth: number = fymonth): number => {
    const [month, yearNum] = dateStr.split('-').map(Number);
    if (month >= fyStartMonth) {
      return yearNum + 1;
    } else {
      return yearNum;
    }
  };

  const processEnergyData = () => {
    let renewableTotal = 0;
    let nonRenewableTotal = 0;

    // Filter data for the specific fiscal year
    const yearData = data.filter((item: any) => {
      if (!item.rp || item.rp.length === 0) return false;
      return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === year);
    });

    console.log(`=== ENERGY MIX FY${year.toString().slice(-2)} DEBUG ===`);
    console.log(`Filtered data for FY${year.toString().slice(-2)}:`, yearData.length, 'items');

    yearData.forEach((item: any) => {
      const value = safeParseValue(item);

      // Check if title contains "non" (case insensitive) for Non-Renewable
      const isNonRenewable = item.title && item.title.toLowerCase().includes('non');
      const isRenewable = item.title && item.title.toLowerCase().includes('renewable') && !item.title.toLowerCase().includes('non');

      console.log(`Item: ${item.title}, Value: ${value}, isRenewable: ${isRenewable}, isNonRenewable: ${isNonRenewable}`);

      if (isNonRenewable) {
        nonRenewableTotal += value;
      } else if (isRenewable) {
        renewableTotal += value;
      }
    });

    console.log(`Renewable total: ${renewableTotal}, Non-Renewable total: ${nonRenewableTotal}`);

    return [
      {
        name: 'Renewable',
        y: renewableTotal,
        color: '#75c3b3' // Green for renewable
      },
      {
        name: 'Non-Renewable',
        y: nonRenewableTotal,
        color: '#ec5624' // Orange for non-renewable
      }
    ].filter(item => item.y > 0); // Only include categories with data
  };

  const chartData = processEnergyData();
  const total = chartData.reduce((sum, item) => sum + item.y, 0);

  if (total === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
        <p className="text-gray-500 font-medium">No Energy Mix Data Available for FY'{year.toString().slice(-2)}</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="mb-4">
        <h4 className="text-lg font-semibold text-gray-900">
          FY'{year.toString().slice(-2)} Energy Mix
        </h4>
        <p className="text-sm text-gray-600">
          {selectedEntity} - Total: {total.toFixed(2)} GJ
        </p>
      </div>

      <div className="h-64">
        <ChartContextMenu title={`FY'${year.toString().slice(-2)} Energy Mix - ${selectedEntity}`}>
          <div id={`energy-mix-fy${year.toString().slice(-2)}-chart`} className="h-full"></div>
        </ChartContextMenu>
      </div>

      {/* Summary Statistics */}
      <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
        <div className="bg-green-50 p-3 rounded-lg">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-[#75c3b3] rounded-full"></div>
            <span className="font-medium">Renewable Energy</span>
          </div>
          <p className="text-lg font-bold text-green-700 mt-1">
            {total > 0 ? ((chartData.find(item => item.name === 'Renewable')?.y || 0) / total * 100).toFixed(1) : 0}%
          </p>
          <p className="text-xs text-gray-600">
            {(chartData.find(item => item.name === 'Renewable')?.y || 0).toFixed(2)} GJ
          </p>
        </div>

        <div className="bg-orange-50 p-3 rounded-lg">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-[#ec5624] rounded-full"></div>
            <span className="font-medium">Non-Renewable Energy</span>
          </div>
          <p className="text-lg font-bold text-orange-700 mt-1">
            {total > 0 ? ((chartData.find(item => item.name === 'Non-Renewable')?.y || 0) / total * 100).toFixed(1) : 0}%
          </p>
          <p className="text-xs text-gray-600">
            {(chartData.find(item => item.name === 'Non-Renewable')?.y || 0).toFixed(2)} GJ
          </p>
        </div>
      </div>
    </div>
  );
};

const RenewableEnergyPieCharts = ({
  selectedEntity,
  data
}: {
  selectedEntity: string;
  data: any[];
}) => {
  const safeParseValue = (item: any): number => {
    const value = item.computedValue;
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return isNaN(numValue) ? 0 : numValue;
  };

  // Helper function to determine fiscal year from date string
  const getFiscalYear = (dateStr: string, fyStartMonth: number = 4): number => {
    const [month, year] = dateStr.split('-').map(Number);
    if (month >= fyStartMonth) {
      return year + 1;
    } else {
      return year;
    }
  };

  // Calculate YTD totals using actual filtered data (indicatorId 481)
  const calculateYTDData = (year: number): PieChartData[] => {
    let renewableTotal = 0;
    let nonRenewableTotal = 0;

    // Filter data for the specific fiscal year
    const yearData = data.filter((item: any) => {
      if (!item.rp || item.rp.length === 0) return false;
      return item.rp.some((monthStr: string) => getFiscalYear(monthStr, 4) === year);
    });

    console.log(`=== ENERGY MIX FY${year.toString().slice(-2)} DEBUG ===`);
    console.log(`Filtered data for FY${year.toString().slice(-2)}:`, yearData.length, 'items');

    yearData.forEach((item: any) => {
      const value = safeParseValue(item);

      // Check if title contains "non" (case insensitive) for Non-Renewable
      const isNonRenewable = item.title && item.title.toLowerCase().includes('non');
      const isRenewable = item.title && item.title.toLowerCase().includes('renewable') && !item.title.toLowerCase().includes('non');

      console.log(`Item: ${item.title}, Value: ${value}, isRenewable: ${isRenewable}, isNonRenewable: ${isNonRenewable}`);

      if (isNonRenewable) {
        nonRenewableTotal += value;
      } else if (isRenewable) {
        renewableTotal += value;
      }
    });

    const total = renewableTotal + nonRenewableTotal;
    console.log(`FY${year.toString().slice(-2)} - Renewable: ${renewableTotal}, Non-Renewable: ${nonRenewableTotal}, Total: ${total}`);

    if (total === 0) {
      return [];
    }

    return [
      {
        name: 'Renewable Energy',
        y: (renewableTotal / total) * 100, // Percentage for chart display
        actualValue: renewableTotal, // Raw value for tooltip
        color: '#75c3b3' // Green for renewable
      },
      {
        name: 'Non-Renewable Energy',
        y: (nonRenewableTotal / total) * 100, // Percentage for chart display
        actualValue: nonRenewableTotal, // Raw value for tooltip
        color: '#ec5624' // Orange for non-renewable
      }
    ];
  };

  const fy25Data = calculateYTDData(2025);
  const fy26Data = calculateYTDData(2026);

  const createPieChartOptions = (data: PieChartData[], title: string): Highcharts.Options => ({
    chart: {
      type: 'pie',
      backgroundColor: 'transparent',
      style: {
        fontFamily: 'Inter, system-ui, sans-serif'
      },
      height: 400
    },
    title: {
      text: title,
      style: {
        fontSize: '16px',
        fontWeight: '600',
        color: 'hsl(210 11% 15%)'
      }
    },
    tooltip: {
      pointFormat: '<b>{point.actualValue:.2f} GJ</b>',
      style: {
        fontSize: '12px'
      }
    },
    accessibility: {
      point: {
        valueSuffix: '' // Remove percentage suffix
      }
    },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        dataLabels: {
          enabled: true,
          format: '<b>{point.name}</b><br>{point.percentage:.1f}%',
          style: {
            fontSize: '12px',
            color: 'hsl(210 11% 15%)',
            textOutline: 'none'
          },
          distance: 20
        },
        showInLegend: true,
        size: '80%',
        innerSize: '40%' // Creates a donut chart for better visual appeal
      }
    },
    legend: {
      align: 'center',
      verticalAlign: 'bottom',
      layout: 'horizontal',
      itemStyle: {
        color: 'hsl(210 11% 15%)',
        fontSize: '12px'
      }
    },
    series: [{
      name: 'Energy Mix',
      type: 'pie',
      data: data
    }],
    credits: {
      enabled: false
    }
  });

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* FY'25 YTD Pie Chart */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-center text-card-foreground">
          FY'{(2025).toString().slice(-2)} Energy Mix
        </h3>
        <ChartContextMenu chartTitle={`FY'${(2025).toString().slice(-2)} Energy Mix`}>
          <div className="h-96 w-full">
            <HighchartsReact
              highcharts={Highcharts}
              options={createPieChartOptions(fy25Data, '')}
              containerProps={{
                style: { width: '100%', height: '100%' }
              }}
            />
          </div>
        </ChartContextMenu>
      </div>

      {/* FY'26 YTD Pie Chart */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-center text-card-foreground">
          FY'{(2026).toString().slice(-2)}Energy Mix
        </h3>
        <ChartContextMenu chartTitle={`FY'${(2026).toString().slice(-2)} Energy Mix`}>
          <div className="h-96 w-full">
            <HighchartsReact
              highcharts={Highcharts}
              options={createPieChartOptions(fy26Data, '')}
              containerProps={{
                style: { width: '100%', height: '100%' }
              }}
            />
          </div>
        </ChartContextMenu>
      </div>
    </div>
  );
};

interface EmissionsDashboardProps {
  data?: any[];
  locationData?: any[];
  isLoading?: boolean;
}

const EmissionsDashboard: React.FC<EmissionsDashboardProps> = ({ data = [], locationData = [], isLoading = false }) => {
  console.log("EmissionsDashboard received data:", data);
  console.log("EmissionsDashboard received locationData:", locationData);

  // Global Entity Filter - applies to all charts
  const [selectedEntity, setSelectedEntity] = useState(locationData.length ? locationData[0].name : 'NA');

  // 3-Tier Location Filters - Default to India (first country in data)
  const [selectedLocationOne, setSelectedLocationOne] = useState(locationData.length ? locationData[0].name : 'NA');
  const [selectedLocationTwo, setSelectedLocationTwo] = useState('All');
  const [selectedLocationThree, setSelectedLocationThree] = useState('All');

  // Keep selectedEntity and selectedLocationOne synchronized
  useEffect(() => {
    setSelectedEntity(selectedLocationOne);
  }, [selectedLocationOne]);
  useEffect(() => {
    if(locationData.length){
      setSelectedLocationOne(locationData[0].name)
      setSelectedEntity(locationData[0].name)
    }
  }, [locationData]);
  useEffect(() => {
    setSelectedLocationOne(selectedEntity);
  }, [selectedEntity]);

  // Reset tier 3 to "All" when tier 2 becomes "All"
  useEffect(() => {
    if (selectedLocationTwo === 'All') {
      setSelectedLocationThree('All');
    }
  }, [selectedLocationTwo]);

  // Get dynamic fuel types from actual data
  const getDynamicFuelTypes = () => {
    // Use original data (not filtered) to get all available fuel types
    if (!data || data.length === 0) {
      console.log("No data available for fuel types");
      return ['All'];
    }

    console.log("Total data items:", data.length);

    const scope1Data = data.filter((item: any) =>
      item.indicatorId === 162
    );

    console.log("Scope 1 data found:", scope1Data.length, "items");

    if (scope1Data.length === 0) {
      console.log("No scope 1 data found with indicatorId 162");
      // Let's also check what indicator IDs are available
      const availableIndicators = [...new Set(data.map((item: any) => item.indicatorId))];
      console.log("Available indicator IDs:", availableIndicators);
      return ['All'];
    }

    // Use the title property specifically as requested
    const fuelTypes = [...new Set(scope1Data.map((item: any) => item.title).filter(Boolean))];

    console.log("Fuel types from title property:", fuelTypes);

    if (fuelTypes.length === 0) {
      console.log("No fuel types found in title property");
      console.log("Sample scope 1 data:", scope1Data.slice(0, 2));
      return ['All'];
    }

    console.log("Final extracted fuel types:", fuelTypes);
    return ['All', ...fuelTypes.filter(type => type !== 'Unknown' && type !== null && type !== undefined)];
  };

  const dynamicScope1FuelTypes = getDynamicFuelTypes();

  // Keep only the type/category filters for each section
  const [scope1SelectedFuelTypes, setScope1SelectedFuelTypes] = useState<string[]>(['All']);

  // Reset fuel type selection if current selection is not in dynamic options
  useEffect(() => {
    const validSelections = scope1SelectedFuelTypes.filter(fuel => dynamicScope1FuelTypes.includes(fuel));
    if (validSelections.length === 0) {
      setScope1SelectedFuelTypes(['All']);
    } else if (validSelections.length !== scope1SelectedFuelTypes.length) {
      setScope1SelectedFuelTypes(validSelections);
    }
  }, [dynamicScope1FuelTypes, scope1SelectedFuelTypes]);

  const [scope3SelectedCategories, setScope3SelectedCategories] = useState<string[]>(['All']);

  const [waterSelectedSource, setWaterSelectedSource] = useState('All');
  const [hazardousWasteSelectedType, setHazardousWasteSelectedType] = useState('All');
  const [nonHazardousWasteSelectedType, setNonHazardousWasteSelectedType] = useState('All');
  const [hazardousWasteDisposalSelectedType, setHazardousWasteDisposalSelectedType] = useState('All');
  const [nonHazardousWasteDisposalSelectedType, setNonHazardousWasteDisposalSelectedType] = useState('All');

  // Process location data to create filter options
  const getLocationOptions = () => {
    if (!locationData || locationData.length === 0) {
      return {
        locationOne: [],
        locationTwo: [],
        locationThree: []
      };
    }

    // Get available countries from locationData
    const locationOneOptions = locationData.map((item: any) => item.name).filter(Boolean) || []
console.log(locationOneOptions)
    // Find selected entity data
    const entityData = locationData.find(item => item.name === selectedEntity);
    if (!entityData || !entityData.locationTwos) {
      return {
        locationOne: locationOneOptions  ,
        locationTwo: ['All'],
        locationThree: ['All']
      };
    }

    // Get Tier 2 options from selected entity's locationTwos
    const locationTwoOptions = ['All', ...entityData.locationTwos.map((item: any) => item.name)];

    // Get Tier 3 options based on selected Tier 2
    let locationThreeOptions = ['All'];
    if (selectedLocationTwo !== 'All') {
      const selectedLocationTwoData = entityData.locationTwos.find((item: any) => item.name === selectedLocationTwo);
      if (selectedLocationTwoData && selectedLocationTwoData.locationThrees) {
        locationThreeOptions = ['All', ...selectedLocationTwoData.locationThrees.map((item: any) => item.name)];
      }
    } else {
      // If "All" is selected for Tier 2, show all Tier 3 options from all Tier 2 locations
      const allLocationThrees = entityData.locationTwos
        .filter((item: any) => item.locationThrees)
        .flatMap((item: any) => item.locationThrees.map((three: any) => three.name));
      locationThreeOptions = ['All', ...Array.from(new Set(allLocationThrees.filter((name: any) => typeof name === 'string'))) as string[]];
    }

    return {
      locationOne: locationOneOptions,
      locationTwo: locationTwoOptions,
      locationThree: locationThreeOptions
    };
  };

  const locationOptions = getLocationOptions();
  // Reset dependent location filters when parent changes
  useEffect(() => {
    if (selectedLocationOne === 'All') {
      setSelectedLocationTwo('All');
      setSelectedLocationThree('All');
    } else {
      const availableLocationTwo = locationOptions.locationTwo;
      if (!availableLocationTwo.includes(selectedLocationTwo)) {
        setSelectedLocationTwo('All');
        setSelectedLocationThree('All');
      }
    }
  }, [selectedLocationOne, locationOptions.locationTwo, selectedLocationTwo]);

  useEffect(() => {
    // Only reset location three if the current selection is not available
    // Don't force reset when location two is "All" - allow direct selection
    const availableLocationThree = locationOptions.locationThree;
    if (!availableLocationThree.includes(selectedLocationThree)) {
      setSelectedLocationThree('All');
    }
  }, [selectedLocationTwo, locationOptions.locationThree, selectedLocationThree]);

  // Filter data based on selected locations and return processed data structure
  const getFilteredData = () => {
    if (!data || data.length === 0) return data;

    let filteredData = data;

    console.log("=== LOCATION FILTERING DEBUG ===");
    console.log("Selected locations:", { selectedLocationOne, selectedLocationTwo, selectedLocationThree });

    // Apply location filters if locationData is available
    if (locationData && locationData.length > 0) {
      const relevantLocationIds: number[] = [];

      // Find selected entity data (instead of hardcoding India)
      const entityData = locationData.find((item: any) => item.name === selectedEntity);
      console.log(`${selectedEntity} data found:`, !!entityData);
      if (entityData) {
        if (selectedLocationTwo === 'All' && selectedLocationThree === 'All') {
          console.log(`Case: ${selectedEntity} → All → All`);
          // Include all location IDs from selected entity
          relevantLocationIds.push(entityData.id);
          entityData.locationTwos?.forEach((locationTwo: any) => {
            relevantLocationIds.push(locationTwo.id);
            locationTwo.locationThrees?.forEach((locationThree: any) => {
              relevantLocationIds.push(locationThree.id);
            });
          });
        } else if (selectedLocationTwo === 'All' && selectedLocationThree !== 'All') {
          console.log(`Case: ${selectedEntity} → All →`, selectedLocationThree);
          // Special case: Tier 2 is "All" but Tier 3 is specific
          // Search for the specific Tier 3 location across all Tier 2 locations
          entityData.locationTwos?.forEach((locationTwo: any) => {
            const foundLocationThree = locationTwo.locationThrees?.find((item: any) => item.name === selectedLocationThree);
            if (foundLocationThree) {
              relevantLocationIds.push(foundLocationThree.id);
            }
          });
        } else if (selectedLocationThree === 'All') {
          console.log(`Case: ${selectedEntity} →`, selectedLocationTwo, "→ All");
          // Include selected Tier 2 and all its Tier 3 locations
          const selectedLocationTwoData = entityData.locationTwos?.find((item: any) => item.name === selectedLocationTwo);
          console.log("Found tier 2 data:", !!selectedLocationTwoData, selectedLocationTwoData?.name);
          console.log("Available tier 2 locations:", entityData.locationTwos?.map((item: any) => item.name));

          if (selectedLocationTwoData) {
            console.log("Tier 2 ID:", selectedLocationTwoData.id);
            relevantLocationIds.push(selectedLocationTwoData.id);

            console.log("Tier 3 locations under", selectedLocationTwo, ":", selectedLocationTwoData.locationThrees?.map((item: any) => ({ name: item.name, id: item.id })));
            selectedLocationTwoData.locationThrees?.forEach((locationThree: any) => {
              relevantLocationIds.push(locationThree.id);
            });
          } else {
            console.log("ERROR: Could not find tier 2 location:", selectedLocationTwo);
          }
        } else {
          console.log(`Case: ${selectedEntity} →`, selectedLocationTwo, "→", selectedLocationThree);
          // Include only selected Tier 3 location
          const selectedLocationTwoData = entityData.locationTwos?.find((item: any) => item.name === selectedLocationTwo);
          if (selectedLocationTwoData) {
            const selectedLocationThreeData = selectedLocationTwoData.locationThrees?.find((item: any) => item.name === selectedLocationThree);
            console.log("Found tier 3 data:", !!selectedLocationThreeData, selectedLocationThreeData?.name);
            if (selectedLocationThreeData) {
              console.log("Tier 3 ID:", selectedLocationThreeData.id);
              relevantLocationIds.push(selectedLocationThreeData.id);
            } else {
              console.log("ERROR: Could not find tier 3 location:", selectedLocationThree, "under", selectedLocationTwo);
            }
          } else {
            console.log("ERROR: Could not find tier 2 location:", selectedLocationTwo);
          }
        }
      }

      console.log("Relevant location IDs:", relevantLocationIds);
      console.log("Sample data locationIds:", data.slice(0, 3).map(item => item.locationId));

      // Debug: Show all unique location IDs in the data
      const allLocationIds = [...new Set(data.map(item => item.locationId))];
      console.log("All unique location IDs in data:", allLocationIds);

      // Debug: Show how many items match each relevant location ID
      relevantLocationIds.forEach(id => {
        const matchingItems = data.filter(item => item.locationId === id);
        const scope1Items = matchingItems.filter(item => item.indicatorId === 162);
        console.log(`Location ID ${id}: ${matchingItems.length} total items, ${scope1Items.length} scope1 items`);
        if (scope1Items.length > 0) {
          const fy24Items = scope1Items.filter(item => {
            if (!item.rp || item.rp.length === 0) return false;
            return item.rp.some((monthStr: string) => {
              const [month, year] = monthStr.split('-').map(Number);
              const fy = month >= 4 ? year + 1 : year;
              return fy === 2024;
            });
          });
          const totalFY24Value = fy24Items.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);
          console.log(`  - FY24 items: ${fy24Items.length}, total value: ${totalFY24Value}`);
        }
      });

      // Safety check: if no relevant location IDs found, return empty array instead of all data
      if (relevantLocationIds.length === 0) {
        console.log("WARNING: No relevant location IDs found - this might be the issue!");
        filteredData = [];
      } else {
        filteredData = data.filter((item: any) =>
          relevantLocationIds.includes(item.locationId)
        );
      }

      console.log("Original data length:", data.length);
      console.log("Filtered data length:", filteredData.length);
    }

    return filteredData;
  };

  const filteredData = getFilteredData();

  // Pre-filter data by indicator types to avoid repeated filtering
  const scope1FilteredData = filteredData.filter((item: any) => item.indicatorId === 162);
  const scope2FilteredData = filteredData.filter((item: any) => item.indicatorId === 163); // Same as Scope 1 as requested
  const scope3FilteredData = filteredData.filter((item: any) => [170,171,172,1642,746,175,177,1135,1641].includes(item.indicatorId) ); // Scope 3 uses different indicator
  const energyMixFilteredData = filteredData.filter((item: any) => item.indicatorId === 481); // Energy Mix uses indicator 481
  const waterConsumptionFilteredData = filteredData.filter((item: any) => item.indicatorId === 202); // Water Consumption uses indicator 202
  const waterDischargeFilteredData = filteredData.filter((item: any) =>
    item.dcfId === 246 || item.dcfId === 247 // Water Discharge: 246 (Withdrawal), 247 (Disposal)
  );
  // For waste data, use original data instead of location-filtered data to avoid over-filtering
  const wasteFilteredData = data.filter((item: any) =>
    item.dcfId === 383 // Waste data: dcfId 383
  );

  console.log("=== PRE-FILTERED DATA SUMMARY ===");
  console.log(`Total filtered data: ${filteredData.length}`);
  console.log(`Scope 1 data: ${scope1FilteredData.length}`);
  console.log(`Scope 2 data: ${scope2FilteredData.length}`);
  console.log(`Scope 3 data: ${scope3FilteredData.length}`);
  console.log(`Energy Mix data: ${energyMixFilteredData.length}`);
  console.log(`Water Consumption data: ${waterConsumptionFilteredData.length}`);
  console.log(`Waste data: ${wasteFilteredData.length}`);
  console.log(`Water Discharge data: ${waterDischargeFilteredData.length}`);

  // Get dynamic Scope 3 categories from indicatorTitle (after scope3FilteredData is defined)
  const getDynamicScope3Categories = () => {
    if (!scope3FilteredData || scope3FilteredData.length === 0) {
      console.log("No scope3 data available for category extraction");
      return ['All'];
    }

    // Extract categories from indicatorTitle field
    const categories = [...new Set(scope3FilteredData.map((item: any) => item.indicatorTitle).filter(Boolean))];

    console.log("Extracted Scope 3 categories from indicatorTitle:", categories);

    if (categories.length === 0) {
      console.log("No categories found in indicatorTitle field");
      return ['All'];
    }

    console.log("Final Scope 3 categories:", categories);
    return ['All', ...categories.filter(category => category !== 'Unknown' && category !== null && category !== undefined)];
  };

  const dynamicScope3Categories = getDynamicScope3Categories();

  // Reset category selection if current selection is not in dynamic options (after dynamicScope3Categories is defined)
  useEffect(() => {
    const validSelections = scope3SelectedCategories.filter(category => dynamicScope3Categories.includes(category));
    if (validSelections.length === 0) {
      setScope3SelectedCategories(['All']);
    } else if (validSelections.length !== scope3SelectedCategories.length) {
      setScope3SelectedCategories(validSelections);
    }
  }, [dynamicScope3Categories, scope3SelectedCategories]);



  // Get location IDs for filtering
  const getLocationIds = () => {
    // Find selected entity data
    const entityData = locationData.find((item: any) => item.name === selectedEntity);
    if (!entityData) return { tier1Id: 103, tier2Id: 0, tier3Id: 0 };

    const tier1Id = entityData.id; // ID for selected entity

    let tier2Id = 0; // 0 means "All"
    if (selectedLocationTwo !== 'All') {
      const tier2Location = entityData.locationTwos?.find((item: any) => item.name === selectedLocationTwo);
      tier2Id = tier2Location?.id || 0;
    }

    let tier3Id = 0; // 0 means "All"
    if (selectedLocationThree !== 'All') {
      if (tier2Id !== 0) {
        // Case: Specific tier2 and specific tier3
        const tier2Location = entityData.locationTwos?.find((item: any) => item.id === tier2Id);
        const tier3Location = tier2Location?.locationThrees?.find((item: any) => item.name === selectedLocationThree);
        tier3Id = tier3Location?.id || 0;
      } else {
        // Case: tier2 is "All" but tier3 is specific - search across all tier2 locations
        for (const tier2Location of entityData.locationTwos || []) {
          const tier3Location = tier2Location.locationThrees?.find((item: any) => item.name === selectedLocationThree);
          if (tier3Location) {
            tier3Id = tier3Location.id;
            tier2Id = tier2Location.id; // Also set the tier2Id for consistency
            break;
          }
        }
      }
    }

    console.log(`=== LOCATION ID CALCULATION ===`);
    console.log(`Input: ${selectedLocationOne} → ${selectedLocationTwo} → ${selectedLocationThree}`);
    console.log(`Output: tier1Id=${tier1Id}, tier2Id=${tier2Id}, tier3Id=${tier3Id}`);

    return { tier1Id, tier2Id, tier3Id };
  };

  // Process Scope 1 data only
  const processScope1Data = (preFilteredData: any[], year: number = 2026, fymonth: number = 4) => {
    const { tier1Id, tier2Id, tier3Id } = getLocationIds();

    // Use pre-filtered scope1 data (already filtered by location and indicatorId)
    console.log("Pre-filtered scope1 data length:", preFilteredData.length);
    console.log("Selected location:", selectedLocationOne, selectedLocationTwo, selectedLocationThree, tier1Id, tier2Id, tier3Id);

    // Debug: Show sample of pre-filtered scope1 data
    console.log("Sample pre-filtered scope1 data:", preFilteredData.slice(0, 3).map(item => ({
      locationId: item.locationId,
      title: item.title,
      computedValue: item.computedValue,
      rp: item.rp
    })));

    // Use pre-filtered data directly (no need for additional filtering)
    const filteredScope1 = preFilteredData;
    console.log("Scope 1 entities:", Array.from(new Set(filteredScope1.map((x: any) => x.entity))));
    // Helper function to safely convert computedValue to number
    const safeParseValue = (value: string | number | null | undefined): number => {
      if (value === null || value === undefined || value === '' || value === '-') {
        return 0;
      }
      const numValue = typeof value === 'string' ? parseFloat(value) : value;
      return isNaN(numValue) ? 0 : numValue;
    };

    // Calculate fiscal years based on passed year parameter
    const currentFY = year;        // FY26 if year = 2026
    const previousFY = year - 1;   // FY25 if year = 2026
    const twoYearsAgoFY = year - 2; // FY24 if year = 2026

    console.log(`=== FISCAL YEAR CALCULATION ===`);
    console.log(`Base year: ${year}, FY start month: ${fymonth}`);
    console.log(`Target FYs: ${twoYearsAgoFY} (bar), ${previousFY} (line1), ${currentFY} (line2)`);

    // Helper function to determine fiscal year from date string
    const getFiscalYear = (dateStr: string, fyStartMonth: number = fymonth): number => {
      const [month, year] = dateStr.split('-').map(Number);
      if (month >= fyStartMonth) {
        return year + 1;
      } else {
        return year;
      }
    };

    // Process data and create chart structure
    const processedData: any = {
      [selectedEntity]: {}
    };

    // Get unique fuel types from filtered data using title property
    const allFilteredData = [...filteredScope1];
    const fuelTypes = [...new Set(allFilteredData.map((item: any) => item.title || 'Unknown'))];

    fuelTypes.forEach(fuelType => {
      const fuelData = allFilteredData.filter((item: any) => (item.title || 'Unknown') === fuelType);

      // Separate data by fiscal year using dynamic fiscal years
      const fy24Data = fuelData.filter((item: any) => {
        if (!item.rp || item.rp.length === 0) return false;
        return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === twoYearsAgoFY);
      });

      const fy25Data = fuelData.filter((item: any) => {
        if (!item.rp || item.rp.length === 0) return false;
        return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === previousFY);
      });

      const fy26Data = fuelData.filter((item: any) => {
        if (!item.rp || item.rp.length === 0) return false;
        return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === currentFY);
      });

      // Calculate FY24 total
      const fy24Total = fy24Data.reduce((sum, item) => sum + safeParseValue(item.computedValue), 0);

      // Debug FY24 calculation
      if (fuelType !== 'Unknown') {
        console.log(`=== FY24 DEBUG for ${fuelType} ===`);
        console.log(`FY24 data items: ${fy24Data.length}`);
        console.log(`FY24 total: ${fy24Total}`);
        console.log('FY24 sample items:', fy24Data.slice(0, 2).map(item => ({
          locationId: item.locationId,
          computedValue: item.computedValue,
          rp: item.rp
        })));
      }

      // Create monthly arrays for FY25 and FY26 - use null for no data
      const fy25Monthly = Array(12).fill(null);
      const fy26Monthly = Array(12).fill(null);
      const fy25HasData = Array(12).fill(false);
      const fy26HasData = Array(12).fill(false);

      // Process FY25 data (previousFY)
      fy25Data.forEach(item => {
        const value = safeParseValue(item.computedValue);
        if (item.rp && item.rp.length > 0) {
          const distributedValue = value / item.rp.length;
          item.rp.forEach((monthStr: string) => {
            const [month, year] = monthStr.split('-').map(Number);
            // FY25 spans from Apr (previousFY-1) to Mar (previousFY)
            if (year === (previousFY - 1) && month >= fymonth) {
              const monthIndex = month - fymonth;
              if (fy25Monthly[monthIndex] === null) {
                fy25Monthly[monthIndex] = 0;
              }
              fy25Monthly[monthIndex] += distributedValue;
              fy25HasData[monthIndex] = true;
            } else if (year === previousFY && month < fymonth) {
              const monthIndex = month + (12 - fymonth);
              if (fy25Monthly[monthIndex] === null) {
                fy25Monthly[monthIndex] = 0;
              }
              fy25Monthly[monthIndex] += distributedValue;
              fy25HasData[monthIndex] = true;
            }
          });
        }
      });

      // Process FY26 data (currentFY)
      fy26Data.forEach(item => {
        const value = safeParseValue(item.computedValue);
        if (item.rp && item.rp.length > 0) {
          const distributedValue = value / item.rp.length;
          item.rp.forEach((monthStr: string) => {
            const [month, year] = monthStr.split('-').map(Number);
            // FY26 spans from Apr (currentFY-1) to Mar (currentFY)
            if (year === (currentFY - 1) && month >= fymonth) {
              const monthIndex = month - fymonth;
              if (fy26Monthly[monthIndex] === null) {
                fy26Monthly[monthIndex] = 0;
              }
              fy26Monthly[monthIndex] += distributedValue;
              fy26HasData[monthIndex] = true;
            } else if (year === currentFY && month < fymonth) {
              const monthIndex = month + (12 - fymonth);
              if (fy26Monthly[monthIndex] === null) {
                fy26Monthly[monthIndex] = 0;
              }
              fy26Monthly[monthIndex] += distributedValue;
              fy26HasData[monthIndex] = true;
            }
          });
        }
      });

      // Get current month to limit future data
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth() + 1; // 1-12
      const currentYear = currentDate.getFullYear();

      // Determine current fiscal month index (0-11)
      let currentFiscalMonthIndex = 11; // Default to show all months

      // Check if we're in FY26 (April 2025 - March 2026)
      if (currentYear === 2025 && currentMonth >= 4) {
        currentFiscalMonthIndex = currentMonth - 4; // Apr=0, May=1, etc.
      } else if (currentYear === 2026 && currentMonth <= 3) {
        currentFiscalMonthIndex = currentMonth + 8; // Jan=9, Feb=10, Mar=11
      } else if (currentYear < 2025 || (currentYear === 2025 && currentMonth < 4)) {
        // We're before FY26 starts, don't show any FY26 data
        currentFiscalMonthIndex = -1;
      }

      // Limit FY26 data to current month (don't show future months)
      if (currentFiscalMonthIndex >= 0) {
        for (let i = currentFiscalMonthIndex + 1; i < 12; i++) {
          fy26Monthly[i] = null;
        }
      } else {
        // If we're before FY26, set all FY26 data to null
        for (let i = 0; i < 12; i++) {
          fy26Monthly[i] = null;
        }
      }

      processedData[selectedEntity][fuelType] = {
        fy24Total,
        monthly: {
          categories: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
          fy25: fy25Monthly,
          fy26: fy26Monthly
        }
      };
    });

    // Return processed data (even if empty - don't fallback to unfiltered data)
    return processedData;
  };

  const scope1ProcessedData = processScope1Data(scope1FilteredData, 2026, 4);

  // Process Scope 2 data (same logic as Scope 1 but without fuel type filtering)
  const processScope2Data = (preFilteredData: any[], year: number = 2026, fymonth: number = 4) => {
    // Calculate fiscal years based on passed year parameter
    const currentFY = year;        // FY26 if year = 2026
    const previousFY = year - 1;   // FY25 if year = 2026
    const twoYearsAgoFY = year - 2; // FY24 if year = 2026

    console.log(`=== SCOPE 2 FISCAL YEAR CALCULATION ===`);
    console.log(`Base year: ${year}, FY start month: ${fymonth}`);
    console.log(`Target FYs: ${twoYearsAgoFY} (bar), ${previousFY} (line1), ${currentFY} (line2)`);

    // Helper function to determine fiscal year from date string
    const getFiscalYear = (dateStr: string, fyStartMonth: number = fymonth): number => {
      const [month, year] = dateStr.split('-').map(Number);
      if (month >= fyStartMonth) {
        return year + 1;
      } else {
        return year;
      }
    };

    // Use pre-filtered scope2 data (already filtered by location and indicatorId)
    console.log("Pre-filtered scope2 data length:", preFilteredData.length);

    const processedData: any = {
      [selectedEntity]: {}
    };

    // Process all data as one category (no fuel type filtering for Scope 2)
    const allFilteredData = [...preFilteredData];

    // Separate data by fiscal year using dynamic fiscal years
    const fy24Data = allFilteredData.filter((item: any) => {
      if (!item.rp || item.rp.length === 0) return false;
      return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === twoYearsAgoFY);
    });

    const fy25Data = allFilteredData.filter((item: any) => {
      if (!item.rp || item.rp.length === 0) return false;
      return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === previousFY);
    });

    const fy26Data = allFilteredData.filter((item: any) => {
      if (!item.rp || item.rp.length === 0) return false;
      return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === currentFY);
    });

    // Calculate FY24 total
    const fy24Total = fy24Data.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);

    console.log(`=== SCOPE 2 FY24 DEBUG ===`);
    console.log(`FY24 data items: ${fy24Data.length}`);
    console.log(`FY24 total: ${fy24Total}`);

    // Create monthly arrays for FY25 and FY26
    const fy25Monthly = Array(12).fill(null);
    const fy26Monthly = Array(12).fill(null);

    // Process FY25 data (previousFY)
    fy25Data.forEach(item => {
      const value = parseFloat(item.computedValue) || 0;
      if (item.rp && item.rp.length > 0) {
        const distributedValue = value / item.rp.length;
        item.rp.forEach((monthStr: string) => {
          const [month, year] = monthStr.split('-').map(Number);
          // FY25 spans from Apr (previousFY-1) to Mar (previousFY)
          if (year === (previousFY - 1) && month >= fymonth) {
            const monthIndex = month - fymonth;
            if (fy25Monthly[monthIndex] === null) {
              fy25Monthly[monthIndex] = 0;
            }
            fy25Monthly[monthIndex] += distributedValue;
          } else if (year === previousFY && month < fymonth) {
            const monthIndex = month + (12 - fymonth);
            if (fy25Monthly[monthIndex] === null) {
              fy25Monthly[monthIndex] = 0;
            }
            fy25Monthly[monthIndex] += distributedValue;
          }
        });
      }
    });

    // Process FY26 data (currentFY)
    fy26Data.forEach(item => {
      const value = parseFloat(item.computedValue) || 0;
      if (item.rp && item.rp.length > 0) {
        const distributedValue = value / item.rp.length;
        item.rp.forEach((monthStr: string) => {
          const [month, year] = monthStr.split('-').map(Number);
          // FY26 spans from Apr (currentFY-1) to Mar (currentFY)
          if (year === (currentFY - 1) && month >= fymonth) {
            const monthIndex = month - fymonth;
            if (fy26Monthly[monthIndex] === null) {
              fy26Monthly[monthIndex] = 0;
            }
            fy26Monthly[monthIndex] += distributedValue;
          } else if (year === currentFY && month < fymonth) {
            const monthIndex = month + (12 - fymonth);
            if (fy26Monthly[monthIndex] === null) {
              fy26Monthly[monthIndex] = 0;
            }
            fy26Monthly[monthIndex] += distributedValue;
          }
        });
      }
    });

    // Store as "Scope 2 Emissions" category (no fuel type breakdown)
    processedData[selectedEntity]["Scope 2 Emissions"] = {
      fy24Total,
      monthly: {
        categories: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
        fy25: fy25Monthly,
        fy26: fy26Monthly
      }
    };

    return processedData;
  };

  const scope2ProcessedData = processScope2Data(scope2FilteredData, 2026, 4);
console.log("Scope 2 processed data:", scope2ProcessedData);
  // Process Scope 3 data with category filtering based on indicatorTitle
  const processScope3Data = (preFilteredData: any[], year: number = 2026, fymonth: number = 4) => {
    // Calculate fiscal years based on passed year parameter
    const currentFY = year;        // FY26 if year = 2026
    const previousFY = year - 1;   // FY25 if year = 2026
    const twoYearsAgoFY = year - 2; // FY24 if year = 2026

    console.log(`=== SCOPE 3 FISCAL YEAR CALCULATION ===`);
    console.log(`Base year: ${year}, FY start month: ${fymonth}`);
    console.log(`Target FYs: ${twoYearsAgoFY} (bar), ${previousFY} (line1), ${currentFY} (line2)`);

    // Helper function to determine fiscal year from date string
    const getFiscalYear = (dateStr: string, fyStartMonth: number = fymonth): number => {
      const [month, year] = dateStr.split('-').map(Number);
      if (month >= fyStartMonth) {
        return year + 1;
      } else {
        return year;
      }
    };

    // Use pre-filtered scope3 data (already filtered by location and indicatorId)
    console.log("Pre-filtered scope3 data length:", preFilteredData.length);

    const processedData: any = {
      [selectedEntity]: {}
    };

    // Get unique categories from indicatorTitle (similar to how fuel types are extracted from title)
    const allFilteredData = [...preFilteredData];
    const categories = [...new Set(allFilteredData.map((item: any) => item.indicatorTitle || 'Unknown'))];

    console.log("Available Scope 3 categories:", categories);

    categories.forEach(category => {
      const categoryData = allFilteredData.filter((item: any) => (item.indicatorTitle || 'Unknown') === category);

      // Separate data by fiscal year using dynamic fiscal years
      const fy24Data = categoryData.filter((item: any) => {
        if (!item.rp || item.rp.length === 0) return false;
        return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === twoYearsAgoFY);
      });

      const fy25Data = categoryData.filter((item: any) => {
        if (!item.rp || item.rp.length === 0) return false;
        return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === previousFY);
      });

      const fy26Data = categoryData.filter((item: any) => {
        if (!item.rp || item.rp.length === 0) return false;
        return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === currentFY);
      });

      // Calculate FY24 total
      const fy24Total = fy24Data.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);

      // Debug FY24 calculation
      if (category !== 'Unknown') {
        console.log(`=== SCOPE 3 FY24 DEBUG for ${category} ===`);
        console.log(`FY24 data items: ${fy24Data.length}`);
        console.log(`FY24 total: ${fy24Total}`);
      }

      // Create monthly arrays for FY25 and FY26
      const fy25Monthly = Array(12).fill(null);
      const fy26Monthly = Array(12).fill(null);

      // Process FY25 data (previousFY)
      fy25Data.forEach(item => {
        const value = parseFloat(item.computedValue) || 0;
        if (item.rp && item.rp.length > 0) {
          const distributedValue = value / item.rp.length;
          item.rp.forEach((monthStr: string) => {
            const [month, year] = monthStr.split('-').map(Number);
            // FY25 spans from Apr (previousFY-1) to Mar (previousFY)
            if (year === (previousFY - 1) && month >= fymonth) {
              const monthIndex = month - fymonth;
              if (fy25Monthly[monthIndex] === null) {
                fy25Monthly[monthIndex] = 0;
              }
              fy25Monthly[monthIndex] += distributedValue;
            } else if (year === previousFY && month < fymonth) {
              const monthIndex = month + (12 - fymonth);
              if (fy25Monthly[monthIndex] === null) {
                fy25Monthly[monthIndex] = 0;
              }
              fy25Monthly[monthIndex] += distributedValue;
            }
          });
        }
      });

      // Process FY26 data (currentFY)
      fy26Data.forEach(item => {
        const value = parseFloat(item.computedValue) || 0;
        if (item.rp && item.rp.length > 0) {
          const distributedValue = value / item.rp.length;
          item.rp.forEach((monthStr: string) => {
            const [month, year] = monthStr.split('-').map(Number);
            // FY26 spans from Apr (currentFY-1) to Mar (currentFY)
            if (year === (currentFY - 1) && month >= fymonth) {
              const monthIndex = month - fymonth;
              if (fy26Monthly[monthIndex] === null) {
                fy26Monthly[monthIndex] = 0;
              }
              fy26Monthly[monthIndex] += distributedValue;
            } else if (year === currentFY && month < fymonth) {
              const monthIndex = month + (12 - fymonth);
              if (fy26Monthly[monthIndex] === null) {
                fy26Monthly[monthIndex] = 0;
              }
              fy26Monthly[monthIndex] += distributedValue;
            }
          });
        }
      });

      processedData[selectedEntity][category] = {
        fy24Total,
        monthly: {
          categories: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
          fy25: fy25Monthly,
          fy26: fy26Monthly
        }
      };
    });

    return processedData;
  };

  const scope3ProcessedData =selectedLocationTwo === 'All' ? processScope3Data(scope3FilteredData, 2026, 4) : []

  // Process Renewable Energy trend data (using same indicatorId 481 as Energy Mix)
  const processRenewableEnergyData = (preFilteredData: any[], year: number = 2026, fymonth: number = 4) => {
    // Calculate fiscal years based on passed year parameter
    const currentFY = year;        // FY26 if year = 2026
    const previousFY = year - 1;   // FY25 if year = 2026
    const twoYearsAgoFY = year - 2; // FY24 if year = 2026

    console.log(`=== RENEWABLE ENERGY FISCAL YEAR CALCULATION ===`);
    console.log(`Base year: ${year}, FY start month: ${fymonth}`);
    console.log(`Target FYs: ${twoYearsAgoFY} (bar), ${previousFY} (line1), ${currentFY} (line2)`);

    // Helper function to determine fiscal year from date string
    const getFiscalYear = (dateStr: string, fyStartMonth: number = fymonth): number => {
      const [month, year] = dateStr.split('-').map(Number);
      if (month >= fyStartMonth) {
        return year + 1;
      } else {
        return year;
      }
    };

    // Filter for renewable energy only
    const renewableData = preFilteredData.filter((item: any) =>
      item.title && item.title.toLowerCase().includes('ren')
    );

    console.log("Renewable energy data length:", renewableData.length);

    const processedData: any = {
      [selectedEntity]: {}
    };

    // Separate data by fiscal year using dynamic fiscal years
    const fy24Data = renewableData.filter((item: any) => {
      if (!item.rp || item.rp.length === 0) return false;
      return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === twoYearsAgoFY);
    });

    const fy25Data = renewableData.filter((item: any) => {
      if (!item.rp || item.rp.length === 0) return false;
      return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === previousFY);
    });

    const fy26Data = renewableData.filter((item: any) => {
      if (!item.rp || item.rp.length === 0) return false;
      return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === currentFY);
    });

    // Calculate FY24 total
    const fy24Total = fy24Data.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);

    console.log(`=== RENEWABLE ENERGY FY24 DEBUG ===`);
    console.log(`FY24 data items: ${fy24Data.length}`);
    console.log(`FY24 total: ${fy24Total}`);

    // Create monthly arrays for FY25 and FY26
    const fy25Monthly = Array(12).fill(null);
    const fy26Monthly = Array(12).fill(null);

    // Process FY25 data (previousFY)
    fy25Data.forEach(item => {
      const value = parseFloat(item.computedValue) || 0;
      if (item.rp && item.rp.length > 0) {
        const distributedValue = value / item.rp.length;
        item.rp.forEach((monthStr: string) => {
          const [month, year] = monthStr.split('-').map(Number);
          // FY25 spans from Apr (previousFY-1) to Mar (previousFY)
          if (year === (previousFY - 1) && month >= fymonth) {
            const monthIndex = month - fymonth;
            if (fy25Monthly[monthIndex] === null) {
              fy25Monthly[monthIndex] = 0;
            }
            fy25Monthly[monthIndex] += distributedValue;
          } else if (year === previousFY && month < fymonth) {
            const monthIndex = month + (12 - fymonth);
            if (fy25Monthly[monthIndex] === null) {
              fy25Monthly[monthIndex] = 0;
            }
            fy25Monthly[monthIndex] += distributedValue;
          }
        });
      }
    });

    // Process FY26 data (currentFY)
    fy26Data.forEach(item => {
      const value = parseFloat(item.computedValue) || 0;
      if (item.rp && item.rp.length > 0) {
        const distributedValue = value / item.rp.length;
        item.rp.forEach((monthStr: string) => {
          const [month, year] = monthStr.split('-').map(Number);
          // FY26 spans from Apr (currentFY-1) to Mar (currentFY)
          if (year === (currentFY - 1) && month >= fymonth) {
            const monthIndex = month - fymonth;
            if (fy26Monthly[monthIndex] === null) {
              fy26Monthly[monthIndex] = 0;
            }
            fy26Monthly[monthIndex] += distributedValue;
          } else if (year === currentFY && month < fymonth) {
            const monthIndex = month + (12 - fymonth);
            if (fy26Monthly[monthIndex] === null) {
              fy26Monthly[monthIndex] = 0;
            }
            fy26Monthly[monthIndex] += distributedValue;
          }
        });
      }
    });

    processedData[selectedEntity]["Renewable Energy"] = {
      fy24Total,
      monthly: {
        categories: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
        fy25: fy25Monthly,
        fy26: fy26Monthly
      }
    };

    return processedData;
  };

  // Process Non-Renewable Energy trend data (using same indicatorId 481 as Energy Mix)
  const processNonRenewableEnergyData = (preFilteredData: any[], year: number = 2026, fymonth: number = 4) => {
    // Calculate fiscal years based on passed year parameter
    const currentFY = year;        // FY26 if year = 2026
    const previousFY = year - 1;   // FY25 if year = 2026
    const twoYearsAgoFY = year - 2; // FY24 if year = 2026

    console.log(`=== NON-RENEWABLE ENERGY FISCAL YEAR CALCULATION ===`);
    console.log(`Base year: ${year}, FY start month: ${fymonth}`);
    console.log(`Target FYs: ${twoYearsAgoFY} (bar), ${previousFY} (line1), ${currentFY} (line2)`);

    // Helper function to determine fiscal year from date string
    const getFiscalYear = (dateStr: string, fyStartMonth: number = fymonth): number => {
      const [month, year] = dateStr.split('-').map(Number);
      if (month >= fyStartMonth) {
        return year + 1;
      } else {
        return year;
      }
    };

    // Filter for non-renewable energy only
    const nonRenewableData = preFilteredData.filter((item: any) =>
      item.title && item.title.toLowerCase().includes('non')
    );

    console.log("Non-renewable energy data length:", nonRenewableData.length);

    const processedData: any = {
      [selectedEntity]: {}
    };

    // Separate data by fiscal year using dynamic fiscal years
    const fy24Data = nonRenewableData.filter((item: any) => {
      if (!item.rp || item.rp.length === 0) return false;
      return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === twoYearsAgoFY);
    });

    const fy25Data = nonRenewableData.filter((item: any) => {
      if (!item.rp || item.rp.length === 0) return false;
      return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === previousFY);
    });

    const fy26Data = nonRenewableData.filter((item: any) => {
      if (!item.rp || item.rp.length === 0) return false;
      return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === currentFY);
    });

    // Calculate FY24 total
    const fy24Total = fy24Data.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);

    console.log(`=== NON-RENEWABLE ENERGY FY24 DEBUG ===`);
    console.log(`FY24 data items: ${fy24Data.length}`);
    console.log(`FY24 total: ${fy24Total}`);

    // Create monthly arrays for FY25 and FY26
    const fy25Monthly = Array(12).fill(null);
    const fy26Monthly = Array(12).fill(null);

    // Process FY25 data (previousFY)
    fy25Data.forEach(item => {
      const value = parseFloat(item.computedValue) || 0;
      if (item.rp && item.rp.length > 0) {
        const distributedValue = value / item.rp.length;
        item.rp.forEach((monthStr: string) => {
          const [month, year] = monthStr.split('-').map(Number);
          // FY25 spans from Apr (previousFY-1) to Mar (previousFY)
          if (year === (previousFY - 1) && month >= fymonth) {
            const monthIndex = month - fymonth;
            if (fy25Monthly[monthIndex] === null) {
              fy25Monthly[monthIndex] = 0;
            }
            fy25Monthly[monthIndex] += distributedValue;
          } else if (year === previousFY && month < fymonth) {
            const monthIndex = month + (12 - fymonth);
            if (fy25Monthly[monthIndex] === null) {
              fy25Monthly[monthIndex] = 0;
            }
            fy25Monthly[monthIndex] += distributedValue;
          }
        });
      }
    });

    // Process FY26 data (currentFY)
    fy26Data.forEach(item => {
      const value = parseFloat(item.computedValue) || 0;
      if (item.rp && item.rp.length > 0) {
        const distributedValue = value / item.rp.length;
        item.rp.forEach((monthStr: string) => {
          const [month, year] = monthStr.split('-').map(Number);
          // FY26 spans from Apr (currentFY-1) to Mar (currentFY)
          if (year === (currentFY - 1) && month >= fymonth) {
            const monthIndex = month - fymonth;
            if (fy26Monthly[monthIndex] === null) {
              fy26Monthly[monthIndex] = 0;
            }
            fy26Monthly[monthIndex] += distributedValue;
          } else if (year === currentFY && month < fymonth) {
            const monthIndex = month + (12 - fymonth);
            if (fy26Monthly[monthIndex] === null) {
              fy26Monthly[monthIndex] = 0;
            }
            fy26Monthly[monthIndex] += distributedValue;
          }
        });
      }
    });

    processedData[selectedEntity]["Non-Renewable Energy"] = {
      fy24Total,
      monthly: {
        categories: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
        fy25: fy25Monthly,
        fy26: fy26Monthly
      }
    };

    return processedData;
  };

  const renewableEnergyProcessedData = processRenewableEnergyData(energyMixFilteredData, 2026, 4);
  const nonRenewableEnergyProcessedData = processNonRenewableEnergyData(energyMixFilteredData, 2026, 4);
  // Pass location filtering parameters to waste processing
  const wasteProcessedData = processWasteDataFromJSON(wasteFilteredData, 2026, 4, selectedEntity, locationData, selectedLocationTwo, selectedLocationThree);

  // Process Water Consumption trend data (using indicatorId 202)
  const processWaterConsumptionData = (preFilteredData: any[], year: number = 2026, fymonth: number = 4) => {
    // Calculate fiscal years based on passed year parameter
    const currentFY = year;        // FY26 if year = 2026
    const previousFY = year - 1;   // FY25 if year = 2026
    const twoYearsAgoFY = year - 2; // FY24 if year = 2026

    console.log(`=== WATER CONSUMPTION FISCAL YEAR CALCULATION ===`);
    console.log(`Base year: ${year}, FY start month: ${fymonth}`);
    console.log(`Target FYs: ${twoYearsAgoFY} (bar), ${previousFY} (line1), ${currentFY} (line2)`);

    // Helper function to determine fiscal year from date string
    const getFiscalYear = (dateStr: string, fyStartMonth: number = fymonth): number => {
      const [month, year] = dateStr.split('-').map(Number);
      if (month >= fyStartMonth) {
        return year + 1;
      } else {
        return year;
      }
    };

    // Use all water consumption data (no source filtering)
    const waterConsumptionData = preFilteredData;

    console.log("Water consumption data length:", waterConsumptionData.length);

    const processedData: any = {
      [selectedEntity]: {}
    };

    // Separate data by fiscal year using dynamic fiscal years
    const fy24Data = waterConsumptionData.filter((item: any) => {
      if (!item.rp || item.rp.length === 0) return false;
      return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === twoYearsAgoFY);
    });

    const fy25Data = waterConsumptionData.filter((item: any) => {
      if (!item.rp || item.rp.length === 0) return false;
      return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === previousFY);
    });

    const fy26Data = waterConsumptionData.filter((item: any) => {
      if (!item.rp || item.rp.length === 0) return false;
      return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === currentFY);
    });

    // Calculate FY24 total
    const fy24Total = fy24Data.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);

    console.log(`=== WATER CONSUMPTION FY24 DEBUG ===`);
    console.log(`FY24 data items: ${fy24Data.length}`);
    console.log(`FY24 total: ${fy24Total}`);

    // Create monthly arrays for FY25 and FY26
    const fy25Monthly = Array(12).fill(null);
    const fy26Monthly = Array(12).fill(null);

    // Process FY25 data (previousFY)
    fy25Data.forEach(item => {
      const value = parseFloat(item.computedValue) || 0;
      if (item.rp && item.rp.length > 0) {
        const distributedValue = value / item.rp.length;
        item.rp.forEach((monthStr: string) => {
          const [month, year] = monthStr.split('-').map(Number);
          // FY25 spans from Apr (previousFY-1) to Mar (previousFY)
          if (year === (previousFY - 1) && month >= fymonth) {
            const monthIndex = month - fymonth;
            if (fy25Monthly[monthIndex] === null) {
              fy25Monthly[monthIndex] = 0;
            }
            fy25Monthly[monthIndex] += distributedValue;
          } else if (year === previousFY && month < fymonth) {
            const monthIndex = month + (12 - fymonth);
            if (fy25Monthly[monthIndex] === null) {
              fy25Monthly[monthIndex] = 0;
            }
            fy25Monthly[monthIndex] += distributedValue;
          }
        });
      }
    });

    // Process FY26 data (currentFY)
    fy26Data.forEach(item => {
      const value = parseFloat(item.computedValue) || 0;
      if (item.rp && item.rp.length > 0) {
        const distributedValue = value / item.rp.length;
        item.rp.forEach((monthStr: string) => {
          const [month, year] = monthStr.split('-').map(Number);
          // FY26 spans from Apr (currentFY-1) to Mar (currentFY)
          if (year === (currentFY - 1) && month >= fymonth) {
            const monthIndex = month - fymonth;
            if (fy26Monthly[monthIndex] === null) {
              fy26Monthly[monthIndex] = 0;
            }
            fy26Monthly[monthIndex] += distributedValue;
          } else if (year === currentFY && month < fymonth) {
            const monthIndex = month + (12 - fymonth);
            if (fy26Monthly[monthIndex] === null) {
              fy26Monthly[monthIndex] = 0;
            }
            fy26Monthly[monthIndex] += distributedValue;
          }
        });
      }
    });

    processedData[selectedEntity]["Water Consumption"] = {
      fy24Total,
      monthly: {
        categories: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
        fy25: fy25Monthly.map(value => typeof value === "number" ? value * 1000 : value),
        fy26: fy26Monthly.map(value => typeof value === "number" ? value * 1000 : value)
      }
    };

    return processedData;
  };

  const waterConsumptionProcessedData = processWaterConsumptionData(waterConsumptionFilteredData, 2026, 4);

  // Process Water Discharge trend data (using dcfId 246 - Withdrawal, 247 - Disposal)
  const processWaterDischargeData = (preFilteredData: any[], year: number = 2026, fymonth: number = 4) => {
    // Calculate fiscal years based on passed year parameter
    const currentFY = year;        // FY26 if year = 2026
    const previousFY = year - 1;   // FY25 if year = 2026
    const twoYearsAgoFY = year - 2; // FY24 if year = 2026

    console.log(`=== WATER DISCHARGE FISCAL YEAR CALCULATION ===`);
    console.log(`Base year: ${year}, FY start month: ${fymonth}`);
    console.log(`Target FYs: ${twoYearsAgoFY} (bar), ${previousFY} (line1), ${currentFY} (line2)`);

    // Helper function to determine fiscal year from date string
    const getFiscalYear = (dateStr: string, fyStartMonth: number = fymonth): number => {
      const [month, year] = dateStr.split('-').map(Number);
      if (month >= fyStartMonth) {
        return year + 1;
      } else {
        return year;
      }
    };

    // Use all water discharge data (dcfId 246 or 247)
    const waterDischargeData = preFilteredData;

    console.log("Water discharge data length:", waterDischargeData.length);

    const processedData: any = {
      [selectedEntity]: {}
    };

    // Separate data by fiscal year using dynamic fiscal years
    const fy24Data = waterDischargeData.filter((item: any) => {
      if (!item.rp || item.rp.length === 0) return false;
      return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === twoYearsAgoFY);
    });

    const fy25Data = waterDischargeData.filter((item: any) => {
      if (!item.rp || item.rp.length === 0) return false;
      return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === previousFY);
    });

    const fy26Data = waterDischargeData.filter((item: any) => {
      if (!item.rp || item.rp.length === 0) return false;
      return item.rp.some((monthStr: string) => getFiscalYear(monthStr, fymonth) === currentFY);
    });

    // Calculate FY24 total
    const fy24Total = fy24Data.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);

    console.log(`=== WATER DISCHARGE FY24 DEBUG ===`);
    console.log(`FY24 data items: ${fy24Data.length}`);
    console.log(`FY24 total: ${fy24Total}`);

    // Create monthly arrays for FY25 and FY26
    const fy25Monthly = Array(12).fill(null);
    const fy26Monthly = Array(12).fill(null);

    // Process FY25 data (previousFY)
    fy25Data.forEach(item => {
      const value = parseFloat(item.computedValue) || 0;
      if (item.rp && item.rp.length > 0) {
        const distributedValue = value / item.rp.length;
        item.rp.forEach((monthStr: string) => {
          const [month, year] = monthStr.split('-').map(Number);
          // FY25 spans from Apr (previousFY-1) to Mar (previousFY)
          if (year === (previousFY - 1) && month >= fymonth) {
            const monthIndex = month - fymonth;
            if (fy25Monthly[monthIndex] === null) {
              fy25Monthly[monthIndex] = 0;
            }
            fy25Monthly[monthIndex] += distributedValue;
          } else if (year === previousFY && month < fymonth) {
            const monthIndex = month + (12 - fymonth);
            if (fy25Monthly[monthIndex] === null) {
              fy25Monthly[monthIndex] = 0;
            }
            fy25Monthly[monthIndex] += distributedValue;
          }
        });
      }
    });

    // Process FY26 data (currentFY)
    fy26Data.forEach(item => {
      const value = parseFloat(item.computedValue) || 0;
      if (item.rp && item.rp.length > 0) {
        const distributedValue = value / item.rp.length;
        item.rp.forEach((monthStr: string) => {
          const [month, year] = monthStr.split('-').map(Number);
          // FY26 spans from Apr (currentFY-1) to Mar (currentFY)
          if (year === (currentFY - 1) && month >= fymonth) {
            const monthIndex = month - fymonth;
            if (fy26Monthly[monthIndex] === null) {
              fy26Monthly[monthIndex] = 0;
            }
            fy26Monthly[monthIndex] += distributedValue;
          } else if (year === currentFY && month < fymonth) {
            const monthIndex = month + (12 - fymonth);
            if (fy26Monthly[monthIndex] === null) {
              fy26Monthly[monthIndex] = 0;
            }
            fy26Monthly[monthIndex] += distributedValue;
          }
        });
      }
    });

    processedData[selectedEntity]["Water Discharge"] = {
      fy24Total,
      monthly: {
        categories: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
        fy25: fy25Monthly,
        fy26: fy26Monthly
      }
    };

    return processedData;
  };

  const waterDischargeProcessedData = processWaterDischargeData(waterDischargeFilteredData, 2026, 4);

  // Debug: Show total FY24 values for current selection
  console.log("=== FINAL PROCESSED DATA DEBUG ===");
  console.log("Selected location:", selectedLocationOne, selectedLocationTwo, selectedLocationThree);
  if (scope1ProcessedData[selectedEntity]) {
    Object.keys(scope1ProcessedData[selectedEntity]).forEach(fuelType => {
      const fy24Total = scope1ProcessedData[selectedEntity][fuelType].fy24Total;
      console.log(`${fuelType} FY24 total: ${fy24Total}`);
    });
  }

  // Display current filter status
  const getFilterStatus = () => {
    const filters = [];
    filters.push(`Country: ${selectedLocationOne}`);
    if (selectedLocationTwo !== 'All') filters.push(`Region: ${selectedLocationTwo}`);
    if (selectedLocationThree !== 'All') filters.push(`Location: ${selectedLocationThree}`);
    return filters.join(' | ');
  };

  // Loading Component
  const LoadingChart = () => (
    <div className="p-6 bg-white flex items-center justify-center h-96">
      <div className="text-center">
        <div className="relative mx-auto mb-4 w-12 h-12">
          <div className="absolute inset-0 rounded-full border-2 border-transparent border-t-[#75c3b3] animate-spin"></div>
          <div className="absolute inset-1 rounded-full border-2 border-transparent border-r-[#ec5624] animate-spin" style={{animationDirection: 'reverse', animationDuration: '1.5s'}}></div>
          <div className="absolute inset-2 rounded-full border-2 border-transparent border-b-[#315875] animate-spin" style={{animationDuration: '2s'}}></div>
        </div>
        <p className="text-gray-500">Loading chart data...</p>
      </div>
    </div>
  );

  // No Data Component
  const NoDataChart = ({ message = "No Data To Display Graph" }: { message?: string }) => (
    <div className="p-6 bg-white flex items-center justify-center h-96">
      <div className="text-center">
        <div className="mx-auto mb-4 w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
          <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </div>
        <p className="text-gray-500 font-medium">{message}</p>
        <p className="text-gray-400 text-sm mt-1">Try adjusting your filters or check back later</p>
      </div>
    </div>
  );

  // Chart Wrapper Component
  const ChartWrapper = ({ children, data, title }: { children: React.ReactNode, data: any, title: string }) => {
    if (isLoading) {
      return <LoadingChart />;
    }

    // Check if data exists and has content
    const hasData = data && (
      (Array.isArray(data) && data.length > 0) ||
      (typeof data === 'object' && Object.keys(data).length > 0)
    );

    if (!hasData) {
      return <NoDataChart message={`No Data Available for ${title}`} />;
    }

    return <>{children}</>;
  };

  // Scope 1 Emissions Chart Component
  const Scope1EmissionsChart: React.FC<{ selectedEntity: string; selectedFuelType: string; data: any[]; year?: number; fymonth?: number; unit?: string }> = ({
    selectedEntity,
    selectedFuelType,
    data = [],
    year = 2026,
    fymonth = 4,
    unit = 'tCO₂e'
  }) => {
    const chartRef = useRef<any>(null);

    // Helper function to safely convert computedValue to number
    const safeParseValue = (value: string | number | null | undefined): number => {
      if (value === null || value === undefined || value === '' || value === '-') {
        return 0;
      }
      const numValue = typeof value === 'string' ? parseFloat(value) : value;
      return isNaN(numValue) ? 0 : numValue;
    };

    // Calculate fiscal years
    const currentFY = year; // FY26
    const previousFY = year - 1; // FY25
    const twoYearsAgoFY = year - 2; // FY24

    // Generate dynamic series names
    const fy24Label = `FY ${twoYearsAgoFY.toString().slice(-2)}`;
    const fy25Label = `FY ${previousFY.toString().slice(-2)}`;
    const fy26Label = `FY ${currentFY.toString().slice(-2)}`;

    // Helper function to determine fiscal year from date string
    const getFiscalYear = (dateStr: string, fyStartMonth: number): number => {
      const [month, year] = dateStr.split('-').map(Number);
      if (month >= fyStartMonth) {
        return year + 1;
      } else {
        return year;
      }
    };

    // Filter data by fiscal year and entity
    const filterDataByFY = (targetFY: number): any[] => {
      return data.filter(item => {
        if (!item.rp || item.rp.length === 0) return false;
        if (item.entity !== selectedEntity) return false;

        // Check if any month in the reporting period belongs to target FY
        return item.rp.some((monthStr: string) => {
          const fy = getFiscalYear(monthStr, fymonth);
          return fy === targetFY;
        });
      });
    };

    // Get data for each fiscal year
    const fy26DataFiltered = filterDataByFY(currentFY); // Current year data
    const fy25DataFiltered = filterDataByFY(previousFY); // Previous year data
    const fy24DataFiltered = filterDataByFY(twoYearsAgoFY); // Two years ago data

    // Check if we have any data
    const hasAnyData = fy26DataFiltered.length > 0 || fy25DataFiltered.length > 0 || fy24DataFiltered.length > 0;

    if (!hasAnyData) {
      return (
        <div className="bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden">
          <div className="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-gray-200">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Scope 1 Emissions - {selectedEntity} ({selectedFuelType})
                </h3>
                <p className="text-sm text-gray-600">FY'25 vs FY'26 Comparison</p>
              </div>
            </div>
          </div>
          <div className="p-6 bg-white flex items-center justify-center h-96">
            <div className="text-center">
              <div className="mx-auto mb-4 w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <p className="text-gray-500 font-medium">No Data Available for Scope 1 Emissions</p>
              <p className="text-gray-400 text-sm mt-1">No data found for {selectedEntity} - {selectedFuelType}</p>
            </div>
          </div>
        </div>
      );
    }

    // Process data for each fiscal year
    const processDataByFY = (fyData: any[]) => {
      const periodMap = new Map<string, number>();

      fyData.forEach(item => {
        const value = safeParseValue(item.computedValue);
        const period = item.reporting_period;

        if (periodMap.has(period)) {
          periodMap.set(period, periodMap.get(period)! + value);
        } else {
          periodMap.set(period, value);
        }
      });

      return periodMap;
    };

    // Create month keys in MM-YYYY format for the fiscal year
    const generateFYMonthKeys = (fy: number, startMonth: number): string[] => {
      const months = [];
      for (let i = 0; i < 12; i++) {
        const monthNum = ((startMonth - 1 + i) % 12) + 1;
        const year = monthNum >= startMonth ? fy - 1 : fy;
        months.push(`${monthNum.toString().padStart(2, '0')}-${year}`);
      }
      return months;
    };

    const currentFYMonthKeys = generateFYMonthKeys(currentFY, fymonth);
    const previousFYMonthKeys = generateFYMonthKeys(previousFY, fymonth);

    // Generate fiscal year months for display
    const fiscalMonths = currentFYMonthKeys.map(monthKey => {
      const [month] = monthKey.split('-');
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const monthName = monthNames[parseInt(month) - 1];
      return monthName;
    });

    // Process current year data (FY26)
    const fy26ProcessedData = processDataByFY(fy26DataFiltered);
    const fy26ChartData = currentFYMonthKeys.map(monthKey => {
      let totalValue = 0;
      let hasData = false;
      const processedPeriods = new Set();

      fy26ProcessedData.forEach((periodValue, reportingPeriod) => {
        const sampleItem = fy26DataFiltered.find(item => item.reporting_period === reportingPeriod);

        if (sampleItem && sampleItem.rp && sampleItem.rp.includes(monthKey)) {
          if (!processedPeriods.has(reportingPeriod)) {
            const distributedValue = periodValue / sampleItem.rp.length;
            totalValue += distributedValue;
            processedPeriods.add(reportingPeriod);
            hasData = true;
          }
        }
      });

      return hasData ? totalValue : null;
    });

    // Process previous year data (FY25)
    const fy25ProcessedData = processDataByFY(fy25DataFiltered);
    const fy25ChartData = previousFYMonthKeys.map(monthKey => {
      let totalValue = 0;
      let hasData = false;
      const processedPeriods = new Set();

      fy25ProcessedData.forEach((periodValue, reportingPeriod) => {
        const sampleItem = fy25DataFiltered.find(item => item.reporting_period === reportingPeriod);

        if (sampleItem && sampleItem.rp && sampleItem.rp.includes(monthKey)) {
          if (!processedPeriods.has(reportingPeriod)) {
            const distributedValue = periodValue / sampleItem.rp.length;
            totalValue += distributedValue;
            processedPeriods.add(reportingPeriod);
            hasData = true;
          }
        }
      });

      return hasData ? totalValue : null;
    });

    // Calculate FY24 total
    const fy24ProcessedData = processDataByFY(fy24DataFiltered);
    const fy24Total = Array.from(fy24ProcessedData.values()).reduce((sum, val) => sum + val, 0);

    // Get current month to limit FY 26 data
    const getCurrentFiscalMonthIndex = (): number => {
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth() + 1; // 1-12
      const currentYear = currentDate.getFullYear();

      for (let i = 0; i < currentFYMonthKeys.length; i++) {
        const [month, year] = currentFYMonthKeys[i].split('-').map(Number);
        if (year === currentYear && month === currentMonth) {
          return i;
        }
        if (year > currentYear || (year === currentYear && month > currentMonth)) {
          return Math.max(0, i - 1);
        }
      }
      return currentFYMonthKeys.length - 1;
    };

    const currentFiscalMonthIndex = getCurrentFiscalMonthIndex();

    // Limit FY 26 data to current month
    const fy26Data = fy26ChartData.map((value, index) => {
      if (index <= currentFiscalMonthIndex) {
        return value;
      } else {
        return null;
      }
    });

    const fy25Data = fy25ChartData;

    // Calculate FY24 data for zone comparison
    const fy24Data = Array(12).fill(fy24Total / 12); // Distribute FY24 total evenly across 12 months for comparison

    // Categories for x-axis - total column + monthly categories
    const totalCategory = [`FY ${twoYearsAgoFY.toString().slice(-2)} Total`];
    const monthlyCategories = fiscalMonths;

    // Chart categories: total + monthly
    const chartCategories = [...totalCategory, ...monthlyCategories];

    // Determine single color for entire FY26 line based on overall performance
    const determineFY26LineColor = () => {
      let improvementCount = 0;
      let worseCount = 0;
      let totalComparisons = 0;

      console.log("=== COLOR DETERMINATION DEBUG ===");
      console.log("FY26 data:", fy26Data);
      console.log("FY25 data:", fy25Data);

      for (let i = 0; i < Math.min(fy26Data.length, fy25Data.length); i++) {
        const currentValue = fy26Data[i];
        const previousValue = fy25Data[i];

        if (currentValue !== null && previousValue !== null && currentValue >= 0 && previousValue >= 0) {
          totalComparisons++;
          if (currentValue <= previousValue) {
            improvementCount++;
            console.log(`Month ${i + 1}: FY26=${currentValue}, FY25=${previousValue} - IMPROVEMENT`);
          } else {
            worseCount++;
            console.log(`Month ${i + 1}: FY26=${currentValue}, FY25=${previousValue} - WORSE`);
          }
        }
      }

      console.log(`Total comparisons: ${totalComparisons}, Improvements: ${improvementCount}, Worse: ${worseCount}`);

      // Determine overall color based on majority
      if (totalComparisons === 0) {
        return '#9CA3AF'; // Grey if no valid comparisons
      } else if (improvementCount >= worseCount) {
        return '#22c55e'; // Green if more improvements or equal
      } else {
        return '#ef4444'; // Red if more deteriorations
      }
    };

    const fy26LineColor = determineFY26LineColor();

    // Create tooltip formatter with unit closure
    const createTooltipFormatter = (tooltipUnit: string) => {
      return function () {
        let s = `<div style="font-weight: 600; color: #111827; margin-bottom: 8px;">${this.category}</div>`;
        this.points?.forEach(function (point: any) {
          const color = point.series.color;
          const value = point.y;

          // Always show the series, but handle null values by showing empty
          if (value === null || value === undefined) {
            s += `<div style="margin-bottom: 4px;">
                    <span style="color: ${color}; font-weight: 600;">${point.series.name}:</span>
                    <span style="font-weight: 600; color: #111827;"></span>
                  </div>`;
          } else {
            // Format value to 2 decimal places
            const formattedValue = Number(value).toFixed(2);
            s += `<div style="margin-bottom: 4px;">
                    <span style="color: ${color}; font-weight: 600;">${point.series.name}:</span>
                    <span style="font-weight: 600; color: #111827;">${formattedValue} ${tooltipUnit}</span>
                  </div>`;
          }
        });
        return s;
      };
    };

    // Check if there's any data to display across all years
    const hasChartData = () => {
      const fy24HasData = fy24Data.some(val => val !== null && val !== undefined);
      const fy25HasData = fy25Data.some(val => val !== null && val !== undefined);
      const fy26HasData = fy26Data.some(val => val !== null && val !== undefined);
      return fy24HasData || fy25HasData || fy26HasData;
    };

    // If no data, show "No Data to Show Graph" message
    if (!hasChartData()) {
      return (
        <div className="bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden">
          <div className="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-gray-200">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Scope 1 Emissions</h3>
                <p className="text-sm text-gray-600">Monthly emissions data by fuel type</p>
              </div>
            </div>
          </div>
          <div className="p-6 bg-white flex items-center justify-center h-96">
            <div className="text-center">
              <div className="mx-auto mb-4 w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Data to Show Graph</h3>
              <p className="text-gray-500">No data available for the selected criteria across all fiscal years.</p>
            </div>
          </div>
        </div>
      );
    }

    const options: Highcharts.Options = {
      chart: {
        backgroundColor: "#ffffff",
        style: {
          fontFamily: '"Inter", "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif',
        },
        height: 450,
        spacingTop: 30,
        spacingBottom: 80,
        spacingLeft: 60,
        spacingRight: 40,
        plotBorderWidth: 0,
        borderRadius: 8,
      },
      title: { text: undefined },
      xAxis: {
        categories: chartCategories,
        title: { text: "", style: { fontSize: "13px", color: "#6B7280", fontWeight: "500" }, margin: 20 },
        labels: {
          style: { fontSize: "12px", color: "#6B7280", fontWeight: "400" },
          rotation: 0,
          y: 25,
          useHTML: true,
          formatter: function () {
            const index = this.pos;

            // Always show the total column (index 0) with special formatting
            if (index === 0) {
              return (
                '<span style="display: flex; flex-direction: column; align-items: center;">'
                + '<span style="font-weight: 600; color: #374151; margin-bottom: 2px;">' + this.value + '</span>'
                + '<span style="font-weight: 600; color: #374151;">Last Year\'s Actual</span>'
                + '</span>'
              );
            }

            // Show all monthly labels
            return String(this.value);
          },
        },
        lineWidth: 1,
        lineColor: "#E5E7EB",
        tickWidth: 1,
        tickColor: "#E5E7EB",
        gridLineWidth: 1,
        gridLineColor: "#F3F4F6",
      },
      yAxis: (() => {
        // Calculate dynamic Y-axis based on all data values
        const allValues = [fy24Total, ...fy26ChartData, ...fy25ChartData].filter(val => val !== null);
        const maxValue = allValues.length > 0 ? Math.max(...allValues) : 100;

        let max: number, tickInterval: number;
        if (maxValue < 10) {
          max = Math.ceil(maxValue);
          tickInterval = 1;
        } else if (maxValue < 100) {
          max = Math.ceil(maxValue / 10) * 10;
          tickInterval = 10;
        } else if (maxValue < 1000) {
          max = Math.ceil(maxValue / 50) * 50;
          tickInterval = 50;
        } else {
          max = Math.ceil(maxValue / 500) * 500;
          tickInterval = 500;
        }

        return {
          min: 0,
          max,
          tickInterval,
          startOnTick: true, // Ensure axis starts exactly at min value
          endOnTick: false, // Allow axis to end at a natural value
          title: {
            text: "tCO₂e (tons of CO₂ equivalent)",
            style: { fontSize: "12px", color: "#374151", fontWeight: "500" },
            margin: 30,
            rotation: 270,
          },
          labels: {
            style: { fontSize: "12px", color: "#6B7280", fontWeight: "400" },
            x: -15,
            formatter: function () { return this.value.toString(); },
          },
          gridLineWidth: 1,
          gridLineColor: "#F3F4F6",
          lineWidth: 1,
          lineColor: "#E5E7EB",
          tickWidth: 1,
          tickColor: "#E5E7EB",
        };
      })(),
      legend: {
        enabled: true,
        layout: "horizontal",
        align: "center",
        verticalAlign: "bottom",
        itemStyle: { fontSize: "12px", color: "#374151", fontWeight: "500" },
        itemHoverStyle: { color: "#111827" },
        itemMarginTop: 5,
        itemMarginBottom: 5,
        symbolHeight: 12,
        symbolWidth: 20,
        symbolRadius: 2,
        y: 15,
        borderWidth: 0,
      },
      tooltip: {
        shared: true,
        backgroundColor: "#FFFFFF",
        borderColor: "#E5E7EB",
        borderRadius: 8,
        borderWidth: 1,
        shadow: {
          color: "rgba(0, 0, 0, 0.1)",
          offsetX: 0,
          offsetY: 2,
          opacity: 0.1,
          width: 4,
        },
        style: { fontSize: "12px", color: "#374151", fontWeight: "400" },
        useHTML: true,
        padding: 12,
        formatter: createTooltipFormatter(unit || 'tCO₂e'),
      },
      plotOptions: {
        column: {
          borderRadius: 3,
          pointPadding: 0.15,
          groupPadding: 0.1,
          borderWidth: 0,
          pointWidth: 40,
          states: {
            hover: {
              brightness: 0.05,
              borderColor: "#374151",
              borderWidth: 1,
            },
          },
        },
        line: {
          marker: {
            enabled: true,
            radius: 4,
            lineWidth: 2,
            lineColor: "#FFFFFF",
          },
          lineWidth: 3,
        },
      },
      series: [
        {
          type: "column",
          name: `${fy24Label} Total Actual`,
          data: [
            { y: fy24Total, color: "#9CA3AF" },
            ...Array(monthlyCategories.length).fill(null),
          ],
          showInLegend: false,
          zIndex: 1,
          borderRadius: 3,
        },
        {
          type: "line",
          name: fy25Label,
          data: [null, ...fy25Data],
          color: "#9CA3AF", // grey for previous year
          marker: {
            symbol: "circle",
            radius: 4,
            lineWidth: 2,
            lineColor: "#FFFFFF",
          },
          lineWidth: 3,
          zIndex: 2,
          dataLabels: {
            enabled: true,
            formatter: function() {
              // Show data labels only for first and last month points (excluding total column)
              const dataIndex = this.x;
              const dataArray = [null, ...fy25Data];

              // Find first and last non-null data points (excluding index 0 which is total column)
              let firstDataIndex = -1;
              let lastDataIndex = -1;

              for (let i = 1; i < dataArray.length; i++) { // Start from 1 to skip total column
                if (dataArray[i] !== null) {
                  if (firstDataIndex === -1) firstDataIndex = i;
                  lastDataIndex = i;
                }
              }

              // Show label only for first and last data points
              if (dataIndex === firstDataIndex || dataIndex === lastDataIndex) {
                return this.y?.toFixed(2);
              }

              return null; // Hide label for other points
            },
            style: {
              color: '#374151',
              fontSize: '11px',
              fontWeight: '600'
            },
            y: -10 // Position above the point
          }
        },
        {
          type: "line",
          name: fy26Label,
          data: [null, ...fy26Data, ...Array(monthlyCategories.length - fy26Data.length).fill(null)],
          color: fy26LineColor, // Single color based on overall performance
          marker: {
            symbol: "circle",
            radius: 4,
            lineWidth: 2,
            lineColor: "#FFFFFF",
          },
          lineWidth: 3,
          zIndex: 3,
        },
      ],
      credits: { enabled: false },
    };

    // Calculate totals for display
    const fy25YTD = fy25Data.slice(0, fy26Data.length).reduce((a: number, b: number) => a + b, 0);
    const fy26YTD = fy26Data.reduce((a: number, b: number) => a + b, 0);
    const difference = fy26YTD - fy25YTD;

    // Context menu handlers
    const handleDownload = () => {
      if (chartRef.current) {
        const chart = chartRef.current.chart as any;
        if (chart.exportChart) {
          chart.exportChart({
            type: 'image/png',
            filename: `scope-1-emissions-fy${year.toString().slice(-2)}-vs-fy${(year - 1).toString().slice(-2)}`
          });
        } else {
          console.log('Export functionality not available');
        }
      }
    };

    const handleCopy = async () => {
      if (chartRef.current) {
        const chart = chartRef.current.chart as any;
        if (chart.getSVG) {
          try {
            const svg = chart.getSVG();
            await navigator.clipboard.writeText(svg);
            console.log('Chart copied to clipboard');
          } catch (err) {
            console.error('Failed to copy chart:', err);
          }
        } else {
          console.log('Copy functionality not available');
        }
      }
    };

    const handlePrint = () => {
      if (chartRef.current) {
        const chart = chartRef.current.chart as any;
        if (chart.print) {
          chart.print();
        } else {
          window.print();
        }
      }
    };

    const handleZoomIn = () => {
      if (chartRef.current) {
        const chart = chartRef.current.chart as any;
        if (chart.zoom) {
          chart.zoom({
            x: chart.plotLeft,
            y: chart.plotTop,
            width: chart.plotWidth * 0.8,
            height: chart.plotHeight * 0.8
          });
        } else {
          console.log('Zoom functionality not available');
        }
      }
    };

    const handleZoomOut = () => {
      if (chartRef.current) {
        const chart = chartRef.current.chart as any;
        if (chart.zoomOut) {
          chart.zoomOut();
        } else {
          console.log('Zoom out functionality not available');
        }
      }
    };

    const handleReset = () => {
      if (chartRef.current) {
        const chart = chartRef.current.chart as any;
        if (chart.zoomOut) {
          chart.zoomOut();
        } else {
          console.log('Reset functionality not available');
        }
      }
    };

    return (
      <div className="bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-slate-50 to-blue-50 px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Scope 1 Emissions - {selectedEntity} ({selectedFuelType})
              </h3>
              <p className="text-sm text-gray-600">FY'25 vs FY'26 Comparison</p>
            </div>
          </div>
        </div>
        <div className="flex flex-row justify-between items-end px-6 pt-4 gap-8">
          <div className="flex flex-row gap-6 justify-content-end">
            <div className="bg-gray-100 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 flex items-center">
              <span className="font-semibold text-gray-900 mr-2">{fy25Label} Total (YTD):</span> {fy25YTD.toFixed(2)} {unit}
            </div>
            <div className="bg-blue-100 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 flex items-center">
              <span className="font-semibold text-blue-900 mr-2">{fy26Label} Total (YTD):</span> {fy26YTD.toFixed(2)} {unit}
            </div>
            <div className={`rounded-lg px-4 py-2 text-sm font-medium text-gray-700 flex items-center ${
              difference < 0 ? 'bg-green-100' : 'bg-red-100'
            }`}>
              <span className={`font-semibold mr-2 ${
                difference < 0 ? 'text-green-900' : 'text-red-900'
              }`}>
                {difference < 0 ? 'Improvement:' : 'Increase:'}
              </span>
              {Math.abs(difference).toFixed(2)} {unit}
            </div>
          </div>
        </div>
        <div className="p-6 bg-white">
          <ChartContextMenu
            chartTitle={`Scope 1 Emissions: FY'${year.toString().slice(-2)} vs FY'${(year - 1).toString().slice(-2)}`}
            onDownload={handleDownload}
            onCopy={handleCopy}
            onPrint={handlePrint}
            onZoomIn={handleZoomIn}
            onZoomOut={handleZoomOut}
            onReset={handleReset}
          >
            <HighchartsReact ref={chartRef} highcharts={Highcharts} options={options} />
          </ChartContextMenu>
        </div>
        <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
          <p className="text-xs text-gray-500 font-medium">
            Green Line: Within baseline target | Red Line: Above baseline target
          </p>
        </div>
      </div>
    );
  };

  return (
    <div className="w-full max-w-none p-6 bg-background min-h-screen">
      <div className="space-y-6">
        {/* Global Entity Filter */}
        <Card className="w-full">
          <CardHeader>
            <div className="flex flex-col gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-2xl font-bold text-foreground">Emissions Dashboard</CardTitle>
                  <p className="text-muted-foreground mt-2">Monitor and track Scope 1,2 & 3 carbon emissions across reporting entities</p>
                </div>
             
              </div>

              {/* 3-Tier Location Filters */}
             {locationData.length ? <div className="flex flex-wrap items-center gap-4 pt-4 border-t border-border">
                <div className="flex items-center gap-3">
                  <label className="text-sm font-medium text-muted-foreground whitespace-nowrap">
                    Country:
                  </label>
                  <Select value={selectedLocationOne} onValueChange={setSelectedLocationOne}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="Select country" />
                    </SelectTrigger>
                    <SelectContent>
                      {locationOptions.locationOne.map((location) => (
                        <SelectItem key={location} value={location}>
                          {location}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center gap-3">
                  <label className="text-sm font-medium text-muted-foreground whitespace-nowrap">
                    Region/City:
                  </label>
                  <Select value={selectedLocationTwo} onValueChange={setSelectedLocationTwo}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Select region" />
                    </SelectTrigger>
                    <SelectContent>
                      {locationOptions.locationTwo.map((location, index) => (
                        <SelectItem key={`location-two-${index}-${location}`} value={location}>
                          {location}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Only show Specific Location when Region/City is not "All" */}
                {selectedLocationTwo !== 'All' && (
                  <div className="flex items-center gap-3">
                    <label className="text-sm font-medium text-muted-foreground whitespace-nowrap">
                      Specific Location:
                    </label>
                    <Select value={selectedLocationThree} onValueChange={setSelectedLocationThree}>
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="Select location" />
                      </SelectTrigger>
                      <SelectContent>
                        {locationOptions.locationThree.map((location, index) => (
                          <SelectItem key={`location-three-${index}-${location}`} value={location}>
                            {location}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}


              </div> : ''}
            </div>
          </CardHeader>
        </Card>



        {/* Scope 1 Emissions */}
        <Card className="w-full">
          <CardHeader>
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <CardTitle className="text-xl text-card-foreground">Scope 1 Emissions Analysis</CardTitle>
              {/* <div className="flex items-center gap-3">
                <label className="text-sm font-medium text-muted-foreground whitespace-nowrap">
                  Fuel Type:
                </label>
                <MultiSelect
                  options={dynamicScope1FuelTypes}
                  selected={scope1SelectedFuelTypes}
                  onChange={setScope1SelectedFuelTypes}
                  placeholder="Select fuel types"
                  className="w-48"
                />
              </div> */}
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <ChartWrapper data={scope1ProcessedData} title="Scope 1 Emissions">
              <EmissionsChart
                title={`Scope 1 Emissions: FY'${(2026).toString().slice(-2)} vs FY'${(2026-1).toString().slice(-2)} Comparison`}
                subtitle={`Reporting Entity: ${selectedEntity} | Fuel Type: ${scope1SelectedFuelTypes.join(', ')}`}
                data={scope1ProcessedData}
                selectedEntity={selectedEntity}
                selectedType={scope1SelectedFuelTypes}
                typeOptions={dynamicScope1FuelTypes}
                year={2026}
                fymonth={4}
                unit="tCO₂e"
                indicationType={0}
              />
            </ChartWrapper>
          </CardContent>
        </Card>

        {/* Scope 2 Emissions */}
        <Card className="w-full">
          <CardHeader>
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <CardTitle className="text-xl text-card-foreground">Scope 2 Emissions Analysis</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <ChartWrapper data={scope2ProcessedData} title="Scope 2 Emissions">
              <EmissionsChart
                title={`Scope 2 Emissions: FY'${(2026).toString().slice(-2)} vs FY'${(2026-1).toString().slice(-2)} Comparison`}
                subtitle={`Reporting Entity: ${selectedEntity} | Location: ${getFilterStatus()}`}
                data={scope2ProcessedData}
                selectedEntity={selectedEntity}
                selectedType={["Scope 2 Emissions"]}
                typeOptions={["Scope 2 Emissions"]}
                year={2026}
                fymonth={4}
                unit="tCO₂e"
                indicationType={0}
              />
            </ChartWrapper>
          </CardContent>
        </Card>

        {/* Scope 3 Emissions */}
     {selectedLocationTwo === 'All' && (
        <Card className="w-full">
          <CardHeader>
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <CardTitle className="text-xl text-card-foreground">Scope 3 Emissions Analysis</CardTitle>
              <div className="flex items-center gap-3">
                <label className="text-sm font-medium text-muted-foreground whitespace-nowrap">
                  Category:
                </label>
                <MultiSelect
                  options={dynamicScope3Categories}
                  selected={scope3SelectedCategories}
                  onChange={setScope3SelectedCategories}
                  placeholder="Select categories"
                  className="w-48"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <ChartWrapper data={scope3ProcessedData} title="Scope 3 Emissions">
              <EmissionsChart
                title={`Scope 3 Emissions: FY'${(2026).toString().slice(-2)} vs FY'${(2026-1).toString().slice(-2)} Comparison`}
                subtitle={`Reporting Entity: ${selectedEntity} | Category: ${scope3SelectedCategories.join(', ')} | Location: ${getFilterStatus()}`}
                data={scope3ProcessedData}
                selectedEntity={selectedEntity}
                selectedType={scope3SelectedCategories}
                typeOptions={dynamicScope3Categories}
                year={2026}
                fymonth={4}
                unit="tCO₂e"
                indicationType={0}
              />
            </ChartWrapper>
          </CardContent>
        </Card>)}

        {/* Renewable Energy vs Non-Renewable Energy Comparison */}
        <Card className="w-full">
          <CardHeader>
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <CardTitle className="text-xl text-card-foreground">Renewable Energy vs Non-Renewable Energy – FY'25 and FY'26 YTD</CardTitle>

            </div>
          </CardHeader>
          <CardContent className="p-6">
            <ChartWrapper data={energyMixFilteredData} title="Renewable vs Non-Renewable Energy">
              <RenewableEnergyPieCharts selectedEntity={selectedEntity} data={energyMixFilteredData} />
            </ChartWrapper>


          </CardContent>
        </Card>

        {/* Renewable Energy Month-on-Month Trend */}
        <Card className="w-full">
          <CardHeader>
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <CardTitle className="text-xl text-card-foreground">Renewable Energy – Month-on-Month Trend</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <ChartWrapper data={renewableEnergyProcessedData} title="Renewable Energy Generation">
              <EmissionsChart
                title={`Renewable Energy Generation: FY'${(2026).toString().slice(-2)} vs FY'${(2026-1).toString().slice(-2)} Comparison`}
                subtitle={`Reporting Entity: ${selectedEntity} | Location: ${getFilterStatus()}`}
                data={renewableEnergyProcessedData}
                selectedEntity={selectedEntity}
                selectedType={["Renewable Energy"]}
                typeOptions={["Renewable Energy"]}
                year={2026}
                fymonth={4}
                yAxisLabel="Energy Generated (GJ)"
                unit="GJ"
                indicationType={1}
              />
            </ChartWrapper>
          </CardContent>
        </Card>

        {/* Non-Renewable Energy Month-on-Month Trend */}
        <Card className="w-full">
          <CardHeader>
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <CardTitle className="text-xl text-card-foreground">Non-Renewable Energy – Month-on-Month Trend</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <ChartWrapper data={nonRenewableEnergyProcessedData} title="Non-Renewable Energy Consumption">
              <EmissionsChart
                title={`Non-Renewable Energy Consumption: FY'${(2026).toString().slice(-2)} vs FY'${(2026-1).toString().slice(-2)} Comparison`}
                subtitle={`Reporting Entity: ${selectedEntity} | Location: ${getFilterStatus()}`}
                data={nonRenewableEnergyProcessedData}
                selectedEntity={selectedEntity}
                selectedType={["Non-Renewable Energy"]}
                typeOptions={["Non-Renewable Energy"]}
                year={2026}
                fymonth={4}
                yAxisLabel="Energy Consumed (GJ)"
                unit="GJ"
                indicationType={0}
              />
            </ChartWrapper>
          </CardContent>
        </Card>

        {/* Water Consumption Month-on-Month Trend */}
        <Card className="w-full">
          <CardHeader>
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <CardTitle className="text-xl text-card-foreground">Water Withdrawl – Month-on-Month Trend</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <ChartWrapper data={waterConsumptionProcessedData} title="Water Withdrawl">
              <EmissionsChart
                title={`Water Consumption: FY'${(2026).toString().slice(-2)} vs FY'${(2026-1).toString().slice(-2)} Comparison`}
                subtitle={`Reporting Entity: ${selectedEntity} | Location: ${getFilterStatus()}`}
                data={waterConsumptionProcessedData}
                selectedEntity={selectedEntity}
                selectedType={["Water Consumption"]}
                typeOptions={["Water Consumption"]}
                year={2026}
                fymonth={4}
                yAxisLabel="Water Consumed (KL)"
                unit="KL"
                indicationType={0}
              />
            </ChartWrapper>
          </CardContent>
        </Card>

        {/* Water Discharge Month-on-Month Trend */}
        {/* <Card className="w-full">
          <CardHeader>
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <CardTitle className="text-xl text-card-foreground">Water Discharge – Month-on-Month Trend</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <ChartWrapper data={waterDischargeProcessedData} title="Water Discharge">
              <EmissionsChart
                title={`Water Discharge: FY'${(2026).toString().slice(-2)} vs FY'${(2026-1).toString().slice(-2)} Comparison`}
                subtitle={`Reporting Entity: ${selectedEntity} | Location: ${getFilterStatus()}`}
                data={waterDischargeProcessedData}
                selectedEntity={selectedEntity}
                selectedType={["Water Discharge"]}
                typeOptions={["Water Discharge"]}
                year={2026}
                fymonth={4}
                yAxisLabel="Water Discharged (ML)"
                unit="ML"
              />
            </ChartWrapper>
          </CardContent>
        </Card> */}

        {/* Waste Generation Charts - Side by Side */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Hazardous Waste Generation */}
          <Card className="w-full">
            <CardHeader>
              <CardTitle className="text-xl text-card-foreground">Hazardous Waste Generation – Month-on-Month Trend</CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <ChartWrapper data={wasteProcessedData.hazardousGeneration} title="Hazardous Waste Generation">
                <EmissionsChart
                  title="Hazardous Waste Generation: FY'26 vs FY'25 Comparison"
                  subtitle={`Reporting Entity: ${selectedEntity} | Location: ${getFilterStatus()}`}
                  data={wasteProcessedData.hazardousGeneration}
                  selectedEntity={selectedEntity}
                  selectedType="All"
                  typeOptions={[]}
                  yAxisLabel="Waste Generated (tonnes)"
                  unit="tonnes"
                  indicationType={0}
                />
              </ChartWrapper>
            </CardContent>
          </Card>

          {/* Non-Hazardous Waste Generation */}
          <Card className="w-full">
            <CardHeader>
              <CardTitle className="text-xl text-card-foreground">Non-Hazardous Waste Generation – Month-on-Month Trend</CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <ChartWrapper data={wasteProcessedData.nonHazardousGeneration} title="Non-Hazardous Waste Generation">
                <EmissionsChart
                  title="Non-Hazardous Waste Generation: FY'26 vs FY'25 Comparison"
                  subtitle={`Reporting Entity: ${selectedEntity} | Location: ${getFilterStatus()}`}
                  data={wasteProcessedData.nonHazardousGeneration}
                  selectedEntity={selectedEntity}
                  selectedType="All"
                  typeOptions={[]}
                  yAxisLabel="Waste Generated (tonnes)"
                  unit="tonnes"
                  indicationType={0}
                />
              </ChartWrapper>
            </CardContent>
          </Card>
        </div>

        {/* Waste Disposal Charts - Side by Side */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Hazardous Waste Disposal */}
          <Card className="w-full">
            <CardHeader>
              <CardTitle className="text-xl text-card-foreground">Hazardous Waste Disposal – Month-on-Month Trend</CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <ChartWrapper data={wasteProcessedData.hazardousDisposal} title="Hazardous Waste Disposal">
                <EmissionsChart
                  title="Hazardous Waste Disposal: FY'26 vs FY'25 Comparison"
                  subtitle={`Reporting Entity: ${selectedEntity} | Location: ${getFilterStatus()}`}
                  data={wasteProcessedData.hazardousDisposal}
                  selectedEntity={selectedEntity}
                  selectedType="All"
                  typeOptions={[]}
                  yAxisLabel="Waste Disposed (tonnes)"
                  unit="tonnes"
                  indicationType={0}
                />
              </ChartWrapper>
            </CardContent>
          </Card>

          {/* Non-Hazardous Waste Disposal */}
          <Card className="w-full">
            <CardHeader>
              <CardTitle className="text-xl text-card-foreground">Non-Hazardous Waste Disposal – Month-on-Month Trend</CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <ChartWrapper data={wasteProcessedData.nonHazardousDisposal} title="Non-Hazardous Waste Disposal">
                <EmissionsChart
                  title="Non-Hazardous Waste Disposal: FY'26 vs FY'25 Comparison"
                  subtitle={`Reporting Entity: ${selectedEntity} | Location: ${getFilterStatus()}`}
                  data={wasteProcessedData.nonHazardousDisposal}
                  selectedEntity={selectedEntity}
                  selectedType="All"
                  typeOptions={[]}
                  yAxisLabel="Waste Disposed (tonnes)"
                  unit="tonnes"
                  indicationType={0}
                />
              </ChartWrapper>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default EmissionsDashboard;
