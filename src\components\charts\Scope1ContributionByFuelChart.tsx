import React, { useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

interface Scope1ContributionByFuelChartProps {
  region?: string;
  enableDrilldown?: boolean;
  useLocationFilter?: boolean;
  data?: any[];
}

const Scope1ContributionByFuelChart: React.FC<Scope1ContributionByFuelChartProps> = ({
  region = "Global",
  enableDrilldown = false,
  useLocationFilter = false,
  data = []
}) => {
  console.log("Scope1 Fuel Data:", data);

  // Process real data for Contribution of Scope 1 Emissions by Fuel
  const fuelData = React.useMemo(() => {
    if (!data || data.length === 0) {
      return [];
    }

    // Group data by fuel type extracted from title
    const fuelGroups = data.reduce((acc, item) => {
      // Extract fuel type from title, split by '<' and take first part
      const label = item.title || '';
      const fuelType = label.split('<')[0].trim();

      if (!fuelType) return acc;

      const computedValue = parseFloat(item.computedValue) || 0;

      if (!acc[fuelType]) {
        acc[fuelType] = {
          name: fuelType,
          value: 0,
          recordCount: 0,
          items: [],
          periods: new Set(),
          unit: item.unitOfMeasure || 'N/A'
        };
      }

      acc[fuelType].value += computedValue;
      acc[fuelType].recordCount += 1;
      acc[fuelType].items.push(item);
      acc[fuelType].periods.add(item.reporting_period);

      return acc;
    }, {});

    console.log("Fuel groups:", fuelGroups);

    // Convert to array and calculate percentages
    const fuelArray = Object.values(fuelGroups);
    const total = fuelArray.reduce((sum, item: any) => sum + item.value, 0);

    // Add colors and percentages
    const colors = [
      "#3b82f6", "#10b981", "#f59e0b", "#ef4444",
      "#8b5cf6", "#06b6d4", "#ec4899", "#84cc16"
    ];

    return fuelArray
      .filter((item: any) => item.value > 0) // Only include fuels with emissions
      .map((item: any, index) => ({
        ...item,
        percentage: total > 0 ? (item.value / total) * 100 : 0,
        color: colors[index % colors.length],
        periods: Array.from(item.periods)
      }))
      .sort((a: any, b: any) => b.value - a.value); // Sort by value descending
  }, [data]);

  const total = fuelData.reduce((sum, item) => sum + item.value, 0);

  // Monthly consumption data
  const monthlyConsumption = {
    categories: ["Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Jan", "Feb"],
    series: [
      {
        name: "Natural Gas",
        data: [112.3, 118.7, 125.4, 132.1, 128.9, 119.3, 134.5, 126.9, 129.6, 127.3, 129.7],
        color: "#3b82f6"
      },
      {
        name: "Diesel",
        data: [58.4, 62.2, 66.6, 71.1, 68.7, 61.3, 72.5, 66.9, 69.6, 67.3, 68.7],
        color: "#10b981"
      },
      {
        name: "Gasoline",
        data: [38.6, 42.8, 45.4, 48.9, 46.3, 42.7, 47.5, 44.1, 46.4, 45.7, 46.3],
        color: "#f59e0b"
      },
      {
        name: "Coal",
        data: [24.6, 26.8, 28.4, 30.9, 29.3, 26.7, 29.5, 27.1, 28.4, 27.7, 28.3],
        color: "#ef4444"
      },
      {
        name: "LPG",
        data: [10.6, 11.8, 12.4, 13.9, 12.3, 11.7, 12.5, 11.1, 12.4, 11.7, 12.3],
        color: "#8b5cf6"
      },
      {
        name: "Other Fuels",
        data: [5.6, 6.8, 7.4, 8.9, 7.3, 6.7, 7.5, 6.1, 7.4, 6.7, 7.3],
        color: "#6b7280"
      }
    ]
  };

  // Process monthly consumption data by fuel type
  const monthlyConsumptionReal = React.useMemo(() => {
    if (!data || data.length === 0) {
      return { categories: [], series: [] };
    }

    // Get all unique periods and sort them
    const allPeriods = [...new Set(data.map(item => item.reporting_period))].sort();
    const categories = allPeriods.map(period => {
      const [month, year] = period.split('-');
      return month;
    });

    // Create series for each fuel type
    const series = fuelData.map(fuel => {
      const fuelValues = categories.map(month => {
        const monthData = data.filter(item => {
          const label = item.title || '';
          const fuelType = label.split('<')[0].trim();
          return item.reporting_period.startsWith(month) && fuelType === fuel.name;
        });
        return monthData.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);
      });

      return {
        name: fuel.name,
        data: fuelValues,
        color: fuel.color
      };
    });

    return { categories, series };
  }, [data, fuelData]);

  const pieChartOptions = {
    chart: {
      type: 'pie',
      backgroundColor: 'transparent',
      height: 450,
    },
    title: {
      text: 'Contribution of Scope 1 Emissions by Fuel Type',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    subtitle: {
      text: `Total: ${total.toFixed(2)} tCO₂e | ${fuelData.length} Fuel Types`,
      style: {
        fontSize: '14px',
        color: '#6b7280'
      }
    },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        dataLabels: {
          enabled: true,
          format: '<b>{point.name}</b>: {point.percentage:.1f}%',
          style: {
            fontSize: '11px'
          }
        },
        showInLegend: true
      }
    },
    series: [{
      name: 'Emissions',
      colorByPoint: true,
      data: fuelData.map(item => ({
        name: item.name,
        y: item.value,
        color: item.color
      }))
    }],
    tooltip: {
      formatter: function() {
        const fuel = fuelData.find(f => f.name === this.point.name);
        return `<b>${this.point.name}</b><br/>
                Emissions: <b>${this.y.toFixed(2)} tCO₂e</b><br/>
                Percentage: <b>${this.percentage.toFixed(1)}%</b><br/>
                Unit: <b>${fuel?.unit || 'N/A'}</b><br/>
                Data Records: <b>${fuel?.recordCount || 0}</b><br/>
                Periods: <b>${fuel?.periods?.length || 0}</b>`;
      }
    },
    legend: {
      align: 'right',
      verticalAlign: 'middle',
      layout: 'vertical',
      itemStyle: {
        fontSize: '12px'
      }
    },
    credits: {
      enabled: false
    }
  };

  const stackedAreaOptions = {
    chart: {
      type: 'area',
      backgroundColor: 'transparent',
      height: 450,
    },
    title: {
      text: 'Monthly Fuel Consumption Trend',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    xAxis: {
      categories: monthlyConsumptionReal.categories.length > 0 ? monthlyConsumptionReal.categories : monthlyConsumption.categories,
      title: {
        text: 'Reporting Period'
      }
    },
    yAxis: {
      title: {
        text: 'Emissions (tCO₂e)'
      },
      stackLabels: {
        enabled: true,
        style: {
          fontWeight: 'bold',
          color: '#374151'
        }
      }
    },
    plotOptions: {
      area: {
        stacking: 'normal',
        lineColor: '#ffffff',
        lineWidth: 1,
        marker: {
          lineWidth: 1,
          lineColor: '#ffffff'
        }
      }
    },
    series: monthlyConsumptionReal.series.length > 0 ? monthlyConsumptionReal.series : monthlyConsumption.series,
    tooltip: {
      formatter: function() {
        return `<b>${this.x}</b><br/>
                ${this.series.name}: <b>${this.y.toFixed(1)} tCO₂e</b><br/>
                Total: <b>${this.point.stackTotal.toFixed(1)} tCO₂e</b>`;
      }
    },
    legend: {
      align: 'center',
      verticalAlign: 'bottom',
      layout: 'horizontal'
    },
    credits: {
      enabled: false
    }
  };

  const [viewType, setViewType] = useState<'pie' | 'area'>('pie');

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="mb-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Contribution of Scope 1 Emissions by Fuel</h3>
            <p className="text-sm text-gray-600">Breakdown of emissions by fuel type consumption</p>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setViewType('pie')}
              className={`px-3 py-1 text-sm rounded ${
                viewType === 'pie' 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Distribution
            </button>
            <button
              onClick={() => setViewType('area')}
              className={`px-3 py-1 text-sm rounded ${
                viewType === 'area' 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Monthly Trend
            </button>
          </div>
        </div>
      </div>
      
      <HighchartsReact
        key={`fuel-chart-${viewType}`}
        highcharts={Highcharts}
        options={viewType === 'pie' ? pieChartOptions : stackedAreaOptions}
      />
      
      {/* Summary Cards */}
    
      {/* No Data Message */}
      {fuelData.length === 0 && (
        <div className="mt-4 text-center py-8">
          <div className="text-gray-500">
            No fuel data available.
            <br />
        
          </div>
        </div>
      )}
    </div>
  );
};

export default Scope1ContributionByFuelChart;
