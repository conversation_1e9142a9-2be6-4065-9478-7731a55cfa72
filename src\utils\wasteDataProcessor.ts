import wasteIndicatorData from '@/data/waste-indicator-data-complete.json';

export interface WasteIndicatorEntry {
  formCategory: number;
  formId: number;
  value: number;
  actualTitle: string;
  title: string;
  category: string;
  disposalMethod: string;
  approverComments: string;
  dateOfApproval: string;
  dcfId: number;
  conversionValue: null;
  entity: string;
  periodFrom: string;
  periodTo: string;
  unitOfMeasure: string;
  dataType: number;
  formType: number;
  uniqueId: string;
  locationId: number;
  level: number;
  reporter: string;
  reportedDate: string;
  reporting_period: string;
  rp: string[];
  reviewedDate: string;
  reporterComments: string;
  reviewer: string;
  efValue: null;
  submitId: number;
  reviewerComments: string;
  approver: string;
  status: string;
  indicatorTitle: string;
  indicatorId: number;
  indicatorUnit: string;
  indicatorType: number;
  emissionFactorName: string;
  emissionFactorValue: string;
  computedValue: string;
}

export interface ProcessedWasteData {
  hazardous: {
    disposal: Record<string, number[]>;
    recovery: Record<string, number[]>;
    total: number[];
  };
  nonHazardous: {
    disposal: Record<string, number[]>;
    recovery: Record<string, number[]>;
    total: number[];
  };
}

// Helper function to get fiscal year from date
const getFiscalYear = (dateStr: string): string => {
  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = date.getMonth() + 1; // 0-based to 1-based
  
  // Fiscal year runs from April to March
  if (month >= 4) {
    return `FY-${year + 1}`;
  } else {
    return `FY-${year}`;
  }
};

// Helper function to determine if waste is hazardous
const isHazardousWaste = (title: string): boolean => {
  return title.toLowerCase().includes('hazardous waste');
};

// Helper function to determine disposal method type
const getDisposalType = (disposalMethod: string): 'disposal' | 'recovery' => {
  return disposalMethod.toLowerCase().includes('recovery') ? 'recovery' : 'disposal';
};

// Helper function to map categories to standardized names
const mapCategory = (category: string, isHazardous: boolean): string => {
  const lowerCategory = category.toLowerCase();
  
  if (isHazardous) {
    if (lowerCategory.includes('bio-medical') || lowerCategory.includes('biomedical')) {
      return 'Bio-medical Waste';
    }
    if (lowerCategory.includes('battery') || lowerCategory.includes('batteries')) {
      return 'Battery Waste';
    }
    if (lowerCategory.includes('oil')) {
      return 'Oil Waste';
    }
    if (lowerCategory.includes('chemical') || lowerCategory.includes('paint') || lowerCategory.includes('thinner')) {
      return 'Chemical Waste';
    }
    if (lowerCategory.includes('electrical') || lowerCategory.includes('weee')) {
      return 'E-Waste';
    }
    return 'Other Hazardous Waste';
  } else {
    if (lowerCategory.includes('plastic')) {
      return 'Plastic Waste';
    }
    if (lowerCategory.includes('metal')) {
      return 'Metal Waste';
    }
    if (lowerCategory.includes('organic') || lowerCategory.includes('food')) {
      return 'Organic Waste';
    }
    if (lowerCategory.includes('electrical') || lowerCategory.includes('weee')) {
      return 'E-Waste';
    }
    return 'Other Non-Hazardous Waste';
  }
};

// Process waste data for charts
export const processWasteData = (): ProcessedWasteData => {
  const data = wasteIndicatorData as WasteIndicatorEntry[];
  
  const result: ProcessedWasteData = {
    hazardous: {
      disposal: {},
      recovery: {},
      total: [0, 0, 0] // FY-2024, FY-2025, FY-2026
    },
    nonHazardous: {
      disposal: {},
      recovery: {},
      total: [0, 0, 0] // FY-2024, FY-2025, FY-2026
    }
  };

  // Group data by fiscal year and category
  data.forEach(entry => {
    const fiscalYear = getFiscalYear(entry.periodFrom);
    const isHazardous = isHazardousWaste(entry.title);
    const disposalType = getDisposalType(entry.disposalMethod);
    const mappedCategory = mapCategory(entry.category, isHazardous);
    const value = parseFloat(entry.computedValue) || 0;
    
    // Keep value in tonnes
    const valueInTonnes = value;
    
    // Determine fiscal year index
    let fyIndex = -1;
    switch (fiscalYear) {
      case 'FY-2024':
        fyIndex = 0;
        break;
      case 'FY-2025':
        fyIndex = 1;
        break;
      case 'FY-2026':
        fyIndex = 2;
        break;
      default:
        return; // Skip unknown fiscal years
    }
    
    const wasteType = isHazardous ? 'hazardous' : 'nonHazardous';
    
    // Initialize category arrays if not exists
    if (!result[wasteType][disposalType][mappedCategory]) {
      result[wasteType][disposalType][mappedCategory] = [0, 0, 0];
    }
    
    // Add value to appropriate category and fiscal year
    result[wasteType][disposalType][mappedCategory][fyIndex] += valueInTonnes;

    // Add to total
    result[wasteType].total[fyIndex] += valueInTonnes;
  });

  return result;
};

// Get categories for hazardous waste
export const getHazardousWasteCategories = (): string[] => {
  const processedData = processWasteData();
  const disposalCategories = Object.keys(processedData.hazardous.disposal);
  const recoveryCategories = Object.keys(processedData.hazardous.recovery);
  
  return [...new Set([...disposalCategories, ...recoveryCategories])];
};

// Get categories for non-hazardous waste
export const getNonHazardousWasteCategories = (): string[] => {
  const processedData = processWasteData();
  const disposalCategories = Object.keys(processedData.nonHazardous.disposal);
  const recoveryCategories = Object.keys(processedData.nonHazardous.recovery);
  
  return [...new Set([...disposalCategories, ...recoveryCategories])];
};

// Get combined data for a category (disposal + recovery)
export const getCombinedCategoryData = (category: string, isHazardous: boolean): number[] => {
  const processedData = processWasteData();
  const wasteType = isHazardous ? 'hazardous' : 'nonHazardous';
  
  const disposalData = processedData[wasteType].disposal[category] || [0, 0, 0];
  const recoveryData = processedData[wasteType].recovery[category] || [0, 0, 0];
  
  return [
    disposalData[0] + recoveryData[0],
    disposalData[1] + recoveryData[1],
    disposalData[2] + recoveryData[2]
  ];
};

// Get monthly data for drilldown
export const getMonthlyWasteData = (category: string, year: string, isHazardous: boolean) => {
  const data = wasteIndicatorData as WasteIndicatorEntry[];
  const targetFY = `FY-${year}`;
  
  const monthlyData: Record<string, number> = {};
  
  data.forEach(entry => {
    const fiscalYear = getFiscalYear(entry.periodFrom);
    const entryIsHazardous = isHazardousWaste(entry.title);
    const mappedCategory = mapCategory(entry.category, entryIsHazardous);
    
    if (fiscalYear === targetFY && entryIsHazardous === isHazardous && mappedCategory === category) {
      const month = entry.reporting_period.split('-')[0];
      const value = parseFloat(entry.computedValue) || 0;
      
      if (!monthlyData[month]) {
        monthlyData[month] = 0;
      }
      monthlyData[month] += value; // Keep in tonnes
    }
  });
  
  return monthlyData;
};
