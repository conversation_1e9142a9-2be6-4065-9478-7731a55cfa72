import React, { useEffect, useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import axios from "axios";

const categories = [
  "Purchased goods and services",
  "Capital goods",
  "Fuel and energy related activities",
  "Upstream transportation and distribution",
  "Waste generation in operation",
  "Business travel",
  "Employee commuting",
  "Downstream transportation and distribution",
  "Use of sold products (avg. 15 yrs)",
  "End-of-life treatment of sold products",
  "Investments (Equity investments in subsidiaries >50%)",
];

interface Scope3EmssionsProps {
  suppliersData: any;
}

const regionOptions = ["India", "Indonesia", "United Kingdom", "Global"];

// Sample data: region -> category -> [FY-2024, FY-2025, FY-2026]
const regionData: Record<string, Record<string, number[]>> = {
  India: {},
  Indonesia: {},
  "United Kingdom": {},
  Global: {},
};

export const Scope3EmissionsByLocationChart = ({
  suppliersData,
}: Scope3EmssionsProps) => {
  const [selectedRegion, setSelectedRegion] = useState("Global");
  const [supplierData, setSupplierData] = useState<any[]>(suppliersData);
  const [locationData, setLocationData] = useState<any[]>([]);
  const [scopeData, setScopeData] = useState<any>({});

  // const data = regionData[selectedRegion] || regionData["Global"];

  const fetchLocationData: any = async (location: string) => {
    try {
      const response = await axios.get(
        "https://api.eisqr.com/user-profiles/289/location-ones?filter=%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationTwos%22%2C%22scope%22%3A%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationThrees%22%7D%5D%7D%7D%5D%7D"
      );

      return response.data;
    } catch (err) {
      console.warn("error:", err);
    }
  };

  const extractAllLocationIds = (locationOne: any): number[] => {
    const ids: number[] = [];
    ids.push(locationOne.id);

    locationOne.locationTwos?.forEach((locationTwo: any) => {
      if (locationTwo.id) ids.push(locationTwo.id);

      locationTwo.locationThrees?.forEach((locationThree: any) => {
        if (locationThree.id) ids.push(locationThree.id);
      });
    });

    return ids;
  };

  useEffect(() => {
    (async () => {
      setSelectedRegion("Global");
      const locationData = await fetchLocationData(selectedRegion);
      setLocationData(locationData);

      setSupplierData(suppliersData);
    })();
  }, []);

  useEffect(() => {
    (async () => {
      let locationIndvData =
        selectedRegion === "Global"
          ? locationData
          : locationData.find((item) => item.name === selectedRegion);

      const allLocationIds = extractAllLocationIds(locationIndvData);

      const finalData =
        selectedRegion === "Global"
          ? supplierData
          : supplierData.filter((item) =>
              allLocationIds.includes(item.locationId)
            );

      // Extract category keys from indicatorTitle
      const keys = Array.from(
        new Set(
          finalData
            .filter((item) => item.indicatorTitle?.includes("Scope 3_Category"))
            .map((item) =>
              item.indicatorTitle
                .split("Scope 3_Category")[1]
                .split(":")[1]
                .trim()
            )
        )
      );

      // Initialize finalObj
      const finalObj: Record<string, Record<string, number>> = {};
      for (const key of keys) {
        finalObj[key] = { 2024: 0, 2025: 0, 2026: 0 };
      }

      // Map fiscal year and accumulate values
      finalData.forEach((item) => {
        const period = item.reporting_period;
        if (!period) return;

        let fiscalYear = "";

        if (period.includes("to")) {
          const match = period.match(/Apr-(\d{4}) to Mar-(\d{4})/);
          if (match) fiscalYear = match[2];
        } else {
          const match = period.match(/([A-Za-z]{3}|\d{2})-(\d{4})/);
          if (match) {
            const monthStr = match[1];
            const year = +match[2];
            const monthMap: Record<string, number> = {
              Jan: 0,
              Feb: 1,
              Mar: 2,
              Apr: 3,
              May: 4,
              Jun: 5,
              Jul: 6,
              Aug: 7,
              Sep: 8,
              Oct: 9,
              Nov: 10,
              Dec: 11,
            };
            const monthNum = isNaN(+monthStr)
              ? monthMap[monthStr]
              : +monthStr - 1;
            fiscalYear =
              monthNum >= 3 ? (year + 1).toString() : year.toString();
          }
        }

        if (!fiscalYear || !["2024", "2025", "2026"].includes(fiscalYear))
          return;

        if (item.indicatorTitle?.includes("Scope 3_Category")) {
          const key = item.indicatorTitle
            .split("Scope 3_Category")[1]
            .split(":")[1]
            .trim();
          if (finalObj[key]) {
            finalObj[key][fiscalYear] += +item.value || 0;
          }
        }
      });

      // Convert to chart-ready structure
      const result: Record<string, number[]> = {};
      for (const [key, val] of Object.entries(finalObj)) {
        result[key] = [val["2024"], val["2025"], val["2026"]].map(
          (v) => +v.toFixed(2)
        );
      }

      setScopeData(result);
    })();
  }, [selectedRegion, supplierData, locationData]);

  const categories = Object.keys(scopeData);

  const series: Highcharts.SeriesColumnOptions[] = categories.map(
    (category, idx) => ({
      type: "column",
      name: category,
      data: scopeData[category],
      color: Highcharts.getOptions().colors?.[idx % 10] || undefined,
    })
  );

  const options: Highcharts.Options = {
    chart: {
      type: "column",
      backgroundColor: "#fff",
      style: { fontFamily: "inherit" },
      scrollablePlotArea: {
        minWidth: 1000,
        scrollPositionX: 0,
      },
    },
    title: { text: undefined },
    xAxis: {
      categories: ["FY-2024", "FY-2025", "FY-2026"],
      title: { text: "Fiscal Year" },
      labels: { style: { fontSize: "14px", color: "#334155" } },
    },
    yAxis: {
      min: 0,
      title: {
        text: "Emissions (Million tCO₂e)",
        style: { fontSize: "14px", color: "#334155" },
      },
      labels: {
        formatter: function () {
          return this.value + "";
        },
        style: { fontSize: "13px", color: "#64748b" },
      },
      gridLineColor: "#e5e7eb",
    },
    legend: {
      enabled: true,
      layout: "vertical",
      align: "right",
      verticalAlign: "middle",
      itemStyle: {
        fontSize: "13px",
        color: "#1e293b",
      },
    },
    tooltip: {
      shared: true,
      headerFormat: "<b>{point.key}</b><br/>",
      pointFormat:
        '<span style="color:{series.color}">{series.name}</span>: <b>{point.y} Million tCO₂e</b><br/>',
    },
    plotOptions: {
      column: {
        grouping: true,
        borderRadius: 4,
        pointPadding: 0.1,
        groupPadding: 0.15,
        dataLabels: {
          enabled: false,
        },
      },
    },
    series: series,
    credits: { enabled: false },
  };

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="mb-4 flex items-center space-x-3">
        <label
          htmlFor="scope3-region-select"
          className="font-medium text-gray-700"
        >
          Country/Region:
        </label>
        <select
          id="scope3-region-select"
          value={selectedRegion}
          onChange={(e) => setSelectedRegion(e.target.value)}
          className="border border-gray-300 rounded px-3 py-1.5 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-400 bg-white shadow-sm"
        >
          {regionOptions.map((opt) => (
            <option key={opt} value={opt}>
              {opt}
            </option>
          ))}
        </select>
      </div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};
