declare module 'jspdf-autotable' {
  import { jsPDF } from 'jspdf';

  interface AutoTableOptions {
    head?: any[][];
    body?: any[][];
    startY?: number;
    styles?: {
      fontSize?: number;
      cellPadding?: number;
      overflow?: string;
      valign?: string;
      halign?: string;
    };
    headStyles?: {
      fillColor?: number[];
      textColor?: number[];
      fontSize?: number;
    };
    bodyStyles?: {
      fillColor?: number[];
      textColor?: number[];
    };
    alternateRowStyles?: {
      fillColor?: number[];
    };
    columnStyles?: {
      [key: number]: {
        cellWidth?: number;
        halign?: string;
      };
    };
    margin?: {
      top?: number;
      right?: number;
      bottom?: number;
      left?: number;
    };
    pageBreak?: string;
    showHead?: string;
    showFoot?: string;
    tableWidth?: string | number;
    theme?: string;
  }

  function autoTable(doc: jsPDF, options: AutoTableOptions): void;
  export default autoTable;
}
