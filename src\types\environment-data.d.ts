declare module "@/data/environment-emissions-data.json" {
  interface EnvironmentEmissionsData {
    [region: string]: {
      scope1: number[];
      scope2: number[];
      scope3: number[];
    };
  }

  interface EnvironmentScope3Data {
    [region: string]: {
      [category: string]: number[];
    };
  }

  interface EnvironmentData {
    environmentEmissionsData: EnvironmentEmissionsData;
    environmentScope3Data: EnvironmentScope3Data;
  }

  const data: EnvironmentData;
  export default data;
} 