import React, { useEffect, useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { EnvironmentDrilldownChart } from "./EnvironmentDrilldownChart";
import {
  EnvironmentLocationFilter,
  EnvironmentLocation,
} from "./EnvironmentLocationFilter";
import axios from "axios";

interface NonRenewableEnergyBreakdownChartProps {
  region: string;
  suppliersData: any[];
  enableDrilldown?: boolean;
  useLocationFilter?: boolean; // true for Environment tab, false for Overview tab
}

const regionOptions = ["India", "Indonesia", "United Kingdom", "Global"];
// Sample places
const places = ["Housr", "Mysore", "Norton", "Bangalore", "Chennai"];

// Sample data: region -> place breakdown
const regionData: Record<string, { name: string; y: number }[]> = {
  India: [
    { name: "Housr", y: 12000 },
    { name: "Mysore", y: 9000 },
    { name: "<PERSON>", y: 8000 },
    { name: "Bangalore", y: 7000 },
    { name: "Chennai", y: 6000 },
  ],
  Indonesia: [
    { name: "<PERSON>us<PERSON>", y: 7000 },
    { name: "Mysore", y: 6500 },
    { name: "<PERSON>", y: 6000 },
    { name: "Jakarta", y: 5500 },
    { name: "Surabaya", y: 5000 },
  ],
  "United Kingdom": [
    { name: "Housr", y: 4000 },
    { name: "Mysore", y: 3500 },
    { name: "Norton", y: 3000 },
    { name: "London", y: 2500 },
    { name: "Manchester", y: 2000 },
  ],
  Global: [
    { name: "Housr", y: 15000 },
    { name: "Mysore", y: 14000 },
    { name: "Norton", y: 13000 },
    { name: "Bangalore", y: 12000 },
    { name: "London", y: 11000 },
  ],
};

// Dummy monthly data for places (12 months)
const monthlyPlaceData: Record<string, number[]> = {
  Housr: [
    1200, 1180, 1190, 1210, 1220, 1230, 1240, 1250, 1260, 1270, 1280, 1290,
  ],
  Mysore: [900, 910, 920, 930, 940, 950, 960, 970, 980, 990, 1000, 1010],
  Norton: [800, 810, 820, 830, 840, 850, 860, 870, 880, 890, 900, 910],
  Bangalore: [700, 710, 720, 730, 740, 750, 760, 770, 780, 790, 800, 810],
  Chennai: [600, 610, 620, 630, 640, 650, 660, 670, 680, 690, 700, 710],
  Jakarta: [550, 560, 570, 580, 590, 600, 610, 620, 630, 640, 650, 660],
  Surabaya: [500, 510, 520, 530, 540, 550, 560, 570, 580, 590, 600, 610],
  London: [
    1100, 1110, 1120, 1130, 1140, 1150, 1160, 1170, 1180, 1190, 1200, 1210,
  ],
  Manchester: [
    2000, 2010, 2020, 2030, 2040, 2050, 2060, 2070, 2080, 2090, 2100, 2110,
  ],
};

export const NonRenewableEnergyBreakdownChart: React.FC<
  NonRenewableEnergyBreakdownChartProps
> = ({
  region,
  suppliersData,
  enableDrilldown = false,
  useLocationFilter = false,
}) => {
  const [location, setLocation] = useState<EnvironmentLocation>({
    country: region || "Global",
    region: "",
    businessUnit: "",
  });
  const [selectedRegion, setSelectedRegion] = useState(region || "Global");
  const [drilldown, setDrilldown] = useState<{
    isActive: boolean;
    category: string;
    year: string;
  } | null>(null);
  // Data selection logic: prefer businessUnit > region > country
  const [supplierData, setSupplierData] = useState<any[]>(suppliersData);
  const [locationData, setLocationData] = useState<any[]>([]);
  const [scopeData, setScopeData] = useState<any>({});

  const data = regionData[selectedRegion] || regionData["Global"];

  const fetchLocationData: any = async (location: string) => {
    try {
      const response = await axios.get(
        "https://api.eisqr.com/user-profiles/289/location-ones?filter=%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationTwos%22%2C%22scope%22%3A%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationThrees%22%7D%5D%7D%7D%5D%7D"
      );

      return response.data;
    } catch (err) {
      console.warn("error:", err);
    }
  };

  const categorizeByCountry = () => {
    const locationIdToCountry: Record<number, string> = {};

    locationData.forEach((country) => {
      const countryName = country.name;

      if (country.id) {
        locationIdToCountry[country.id] = countryName;
      }

      country.locationTwos?.forEach((loc2: any) => {
        if (loc2.id) {
          locationIdToCountry[loc2.id] = countryName;
        }

        loc2.locationThrees?.forEach((loc3: any) => {
          if (loc3.id) {
            locationIdToCountry[loc3.id] = countryName;
          }
        });
      });
    });

    console.log(locationIdToCountry);

    const countryCounts: Record<string, number> = {};

    supplierData
      .filter((item) => item.title.split(">")[1] === "Non-Renewable")
      .forEach((item) => {
        const locationId = item.locationId;
        const country = locationIdToCountry[locationId];

        if (country) {
          countryCounts[country] = (countryCounts[country] || 0) + 1;
        } else {
          countryCounts["Unknown"] = (countryCounts["Unknown"] || 0) + 1;
        }
      });

    console.log(countryCounts, locationIdToCountry);

    return countryCounts;
  };

  useEffect(() => {
    (async () => {
      setSelectedRegion("Global");
      const locationData = await fetchLocationData(selectedRegion);
      setLocationData(locationData);

      setSupplierData(suppliersData);
    })();
  }, []);

  useEffect(() => {
    (async () => {
      let finalData = [];
      let categoryData = categorizeByCountry();
      let keys = Object.keys(categoryData);

      keys.forEach((key) => {
        finalData.push({
          name: key,
          y: categoryData[key],
        });
      });

      console.log(finalData);

      setScopeData(finalData);
    })();
  }, [supplierData]);

  const handleDrilldown = (place: string) => {
    if (!enableDrilldown) return;
    setDrilldown({ isActive: true, category: place, year: "2024" });
  };

  const handleBack = () => setDrilldown(null);

  // if (enableDrilldown && drilldown?.isActive && drilldown.category) {
  //   return (
  //     <EnvironmentDrilldownChart
  //       metricType="nonrenewable"
  //       region={location.country}
  //       year={drilldown.year}
  //       category={drilldown.category}
  //       onBack={handleBack}
  //       monthlyDrilldownData={{
  //         nonrenewable: {
  //           [location.country]: {
  //             [drilldown.year]: monthlyPlaceData[drilldown.category] || [],
  //           },
  //         },
  //       }}
  //     />
  //   );
  // }

  const options: Highcharts.Options = {
    chart: {
      type: "pie",
      backgroundColor: "#fff",
      style: { fontFamily: "inherit" },
    },
    title: { text: undefined },
    tooltip: {
      pointFormat: "<b>{point.y} GJ</b> ({point.percentage:.1f}%)",
    },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: enableDrilldown ? "pointer" : "default",
        dataLabels: {
          enabled: true,
          format: "<b>{point.name}</b>: {point.percentage:.1f} %",
        },
        point: {
          events: {
            click: function () {
              if (enableDrilldown) handleDrilldown(this.name);
            },
          },
        },
      },
    },
    series: [
      {
        type: "pie",
        name: "Non-Renewable Energy Consumption",
        data: scopeData,
      },
    ],
    credits: { enabled: false },
  };

  return (
    <div className="bg-white rounded-lg shadow p-4">
      {useLocationFilter ? (
        <EnvironmentLocationFilter value={location} onChange={setLocation} />
      ) : (
        // <div className="mb-4 flex items-center space-x-3">
        //   <label
        //     htmlFor="nonrenewable-energy-region-select"
        //     className="font-medium text-gray-700"
        //   >
        //     Country/Region:
        //   </label>
        //   <select
        //     id="nonrenewable-energy-region-select"
        //     value={selectedRegion}
        //     onChange={(e) => setSelectedRegion(e.target.value)}
        //     className="border border-gray-300 rounded px-3 py-1.5 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-400 bg-white shadow-sm"
        //   >
        //     {Object.keys(regionData).map((opt) => (
        //       <option key={opt} value={opt}>
        //         {opt}
        //       </option>
        //     ))}
        //   </select>
        // </div>
        <></>
      )}
      {enableDrilldown && (
        <div className="mb-2 text-sm text-gray-600">
          💡 Click on any bar to view monthly breakdown
        </div>
      )}
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};
