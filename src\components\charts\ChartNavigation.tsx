import { useEffect, useState } from "react";
import { BarChart3, Users, Shield, Leaf } from "lucide-react";
import MetricCard from "@/components/MetricCard";
import MetricDetails from "@/components/MetricDetails";
import FilterBar, { FilterState } from "@/components/FilterBar";
import axios from "axios";
import { Skeleton } from "../ui/skeleton";
import { format, parse } from "date-fns";
interface ChartNavigationProps {
  activeView: string;
  onViewChange: (view: string) => void;
  selectedMetric: string;
  onMetricChange: (metric: string) => void;
  suppliersData: any;
}

const ChartNavigation = ({
  activeView,
  onViewChange,
  selectedMetric,
  onMetricChange,
  suppliersData,
}: ChartNavigationProps) => {
  const [openDetailsId, setOpenDetailsId] = useState<string | null>(null);
  const [supplierData, setSupplierData] = useState<any[]>(suppliersData);
  const [overviewMetrics, setOverviewMetrics] = useState<any[]>([
    {
      id: "chart-overview-direct-emissions",
      title: "YTD Direct Emissions (Scope 1 + Scope 2)",
      value: "2,342.23",
      unit: "t CO₂e",
      target: 10556,
      targetPercentage: 40,
      trend: [
        2800, 2650, 2500, 2400, 2350, 2300, 2280, 2250, 2300, 2320, 2340, 2342,
      ],
      isImproving: true,
    },
    {
      id: "chart-overview-energy-consumption",
      title: "YTD Energy Consumption",
      value: "12,847.5",
      unit: "MWh",
      target: 15000,
      targetPercentage: 14.3,
      trend: [
        11000, 11500, 12000, 12200, 12400, 12600, 12650, 12700, 12750, 12800,
        12820, 12847,
      ],
      isImproving: false,
    },
    {
      id: "chart-overview-water-usage",
      title: "YTD Water Usage",
      value: "15,234",
      unit: "m³",
      target: 18000,
      targetPercentage: 15.4,
      trend: [
        16000, 15800, 15600, 15500, 15400, 15350, 15300, 15280, 15250, 15240,
        15235, 15234,
      ],
      isImproving: true,
    },
    {
      id: "chart-overview-waste-generated",
      title: "YTD Waste Generated",
      value: "456.7",
      unit: "tonnes",
      target: 600,
      targetPercentage: 23.9,
      trend: [580, 560, 540, 520, 500, 490, 480, 470, 465, 460, 458, 456],
      isImproving: true,
    },
  ]);
  const [selectedPeriods, setSelectedPeriods] = useState<
    Record<string, string>
  >({});

  // Separate filter states for each tab
  const [tabFilters, setTabFilters] = useState<Record<string, FilterState>>({
    overview: {
      country: "all",
      businessUnit: "all",
      site: "all",
      year: "2024",
    },
    environment: {
      country: "all",
      businessUnit: "all",
      site: "all",
      year: "2024",
    },
  });

  const groupByMonth = (data) => {
    const monthMap: Record<string, number> = {};

    data.forEach((item) => {
      const date = new Date(item.reportedDate);
      const value = parseFloat(item.computedValue);

      if (!isNaN(date.getTime()) && !isNaN(value)) {
        const month = format(date, "MMM");
        monthMap[month] = (monthMap[month] || 0) + value;
      }
    });

    const MONTH_ORDER = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    return MONTH_ORDER.map((month) => ({
      name: month,
      value: Math.round((monthMap[month] || 0) * 1000) / 1000,
    }));
  };

  useEffect(() => {
    (async () => {
      const mergedData = supplierData;

      setSupplierData(mergedData);

      const groupedTrend = groupByMonth(mergedData);
      const trend = groupedTrend.map((d) => d.value);
      let currentOverviewMetrics = overviewMetrics;
      currentOverviewMetrics[0]["unit"] = mergedData[0]["indicatorUnit"];
      currentOverviewMetrics[0]["value"] = groupedTrend
        .reduce((acc, sum) => acc + sum.value, 0)
        .toString();
      currentOverviewMetrics[0]["trend"] = trend;

      const groupedTrend163 = groupByMonth(
        mergedData.filter((item) => item.indicatorId == 163)
      );
      const trend163 = groupedTrend163.map((d) => d.value);
      currentOverviewMetrics[1]["unit"] = mergedData.filter(
        (item) => item.indicatorId == 163
      )[0]["unitOfMeasure"];
      currentOverviewMetrics[1]["value"] = groupedTrend163
        .reduce((acc, sum) => acc + sum.value, 0)
        .toString();
      currentOverviewMetrics[1]["trend"] = trend163;

      setOverviewMetrics(currentOverviewMetrics);
    })();
  }, [supplierData, tabFilters]);

  const environmentMetrics = [
    {
      id: "chart-environment-scope1-emissions",
      title: "YTD Scope 1 Emissions",
      value: "842.15",
      unit: "t CO₂e",
      target: 1200,
      targetPercentage: 29.8,
      trend: [1100, 1050, 1000, 950, 920, 900, 880, 860, 855, 850, 845, 842],
      isImproving: true,
    },
    {
      id: "chart-environment-scope2-emissions",
      title: "YTD Scope 2 Emissions",
      value: "1,245.8",
      unit: "t CO₂e",
      target: 1800,
      targetPercentage: 30.8,
      trend: [
        1600, 1550, 1500, 1450, 1400, 1350, 1320, 1300, 1280, 1260, 1250, 1245,
      ],
      isImproving: true,
    },
    {
      id: "chart-environment-scope3-emissions",
      title: "YTD Scope 3 Emissions",
      value: "254.28",
      unit: "t CO₂e",
      target: 350,
      targetPercentage: 27.3,
      trend: [320, 310, 300, 290, 280, 275, 270, 265, 260, 258, 256, 254],
      isImproving: true,
    },
    {
      id: "chart-environment-renewable-energy",
      title: "Renewable Energy Usage",
      value: "34.2",
      unit: "%",
      target: 50,
      targetPercentage: 31.6,
      trend: [20, 22, 24, 26, 28, 29, 30, 31, 32, 33, 33.5, 34.2],
      isImproving: true,
    },
  ];

  const getMetricsForTab = (tab: string) => {
    switch (tab) {
      case "environment":
        return environmentMetrics;
      default:
        return overviewMetrics;
    }
  };

  const handleToggleDetails = (metricId: string) => {
    setOpenDetailsId(openDetailsId === metricId ? null : metricId);
  };

  const handlePeriodChange = (metricId: string, period: string) => {
    setSelectedPeriods((prev) => ({
      ...prev,
      [metricId]: period,
    }));
  };

  const getSelectedPeriod = (metricId: string) => {
    return selectedPeriods[metricId] || "1Y";
  };

  // Handle filter changes for the current tab
  const handleFilterChange = (filterType: keyof FilterState, value: string) => {
    setTabFilters((prev) => ({
      ...prev,
      [activeView]: {
        ...prev[activeView],
        [filterType]: value,
      },
    }));
  };

  const views = [
    {
      id: "overview",
      label: "Overview",
      icon: BarChart3,
      color: "bg-blue-500",
      gradient: "from-blue-500 to-blue-600",
      value: "94.2%",
      change: "+5.2%",
      isPositive: true,
      trendData: [
        { x: 1, y: 85 },
        { x: 2, y: 87 },
        { x: 3, y: 89 },
        { x: 4, y: 91 },
        { x: 5, y: 88 },
        { x: 6, y: 92 },
        { x: 7, y: 94 },
      ],
    },
    {
      id: "environment",
      label: "Environment",
      icon: Leaf,
      color: "bg-green-500",
      gradient: "from-green-500 to-emerald-600",
      value: "87.8%",
      change: "+8.1%",
      isPositive: true,
      trendData: [
        { x: 1, y: 75 },
        { x: 2, y: 78 },
        { x: 3, y: 82 },
        { x: 4, y: 85 },
        { x: 5, y: 83 },
        { x: 6, y: 86 },
        { x: 7, y: 88 },
      ],
    },
    {
      id: "environment-new",
      label: "Environment (New)",
      icon: Leaf,
      color: "bg-emerald-500",
      gradient: "from-emerald-500 to-green-600",
      value: "--",
      change: "--",
      isPositive: true,
      trendData: [],
    },
  ];

  return (
    <div className="space-y-6">
      {/* Clean Tab Navigation */}
      <div className="space-y-4">
        {/* Modern Tab Navigation */}
        <div className="flex space-x-6 bg-gray-50 px-6 py-1 border-b border-gray-200">
          {views.map((view) => {
            const isActive = activeView === view.id;

            return (
              <button
                key={view.id}
                onClick={() => onViewChange(view.id)}
                className={`
                  px-4 py-3 text-sm font-medium transition-all duration-200 relative
                  ${
                    isActive
                      ? "text-blue-600 border-b-2 border-blue-600"
                      : "text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300"
                  }
                `}
              >
                {view.label}
              </button>
            );
          })}
        </div>

        {/* Tab Content with Metric Boxes */}
        <div className="space-y-6 mt-6">
          {/* Filter Bar for each tab */}
          {activeView !== "environment-new" && (
            <>
              <FilterBar
                compact={true}
                showTitle={false}
                filterState={tabFilters[activeView]}
                onFilterChange={handleFilterChange}
              />

              {/* Headline Metrics */}
              <div className="grid grid-cols-4 gap-4">
                {getMetricsForTab(activeView).map((metric) => (
                  <MetricCard
                    key={metric.id}
                    {...metric}
                    showDetails={openDetailsId === metric.id}
                    onToggleDetails={() => handleToggleDetails(metric.id)}
                    selectedPeriod={getSelectedPeriod(metric.id)}
                    onPeriodChange={(period) =>
                      handlePeriodChange(metric.id, period)
                    }
                  />
                ))}
              </div>

              {/* Details Section - Rendered below the grid when a card is selected */}
              {openDetailsId &&
                (() => {
                  const selectedMetric = getMetricsForTab(activeView).find(
                    (m) => m.id === openDetailsId
                  );
                  return selectedMetric ? (
                    <MetricDetails
                      title={selectedMetric.title}
                      value={selectedMetric.value}
                      unit={selectedMetric.unit}
                      targetPercentage={selectedMetric.targetPercentage}
                      trend={selectedMetric.trend}
                      isImproving={selectedMetric.isImproving}
                      selectedPeriod={getSelectedPeriod(openDetailsId)}
                    />
                  ) : null;
                })()}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChartNavigation;
