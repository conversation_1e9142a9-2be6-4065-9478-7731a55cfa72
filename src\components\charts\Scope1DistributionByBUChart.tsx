import React, { useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

interface Scope1DistributionByBUChartProps {
  region?: string;
  enableDrilldown?: boolean;
  useLocationFilter?: boolean;
  BUList?: any[];
  data?: any[];
}

const Scope1DistributionByBUChart: React.FC<Scope1DistributionByBUChartProps> = ({
  region = "Global",
  enableDrilldown = false,
  useLocationFilter = false,
  BUList = [],
  data = []
}) => {
  console.log("BUList:", BUList);
  console.log("Data:", data);

  // Process real data - filter by level 3 and group by entity (BU)
  const processedBUData = React.useMemo(() => {
    if (!data || data.length === 0) {
      return [];
    }

    // Filter data by level 3 (BU level)
    const level3Data = data.filter(item => item.level === 3);

    // Group by entity (Business Unit)
    const groupedData = level3Data.reduce((acc, item) => {
      const entity = item.entity;
      const computedValue = parseFloat(item.computedValue) || 0;

      if (!acc[entity]) {
        acc[entity] = {
          name: entity,
          value: 0,
          locationId: item.locationId,
          count: 0
        };
      }

      acc[entity].value += computedValue;
      acc[entity].count += 1;

      return acc;
    }, {});

    // Convert to array and calculate percentages
    const buArray = Object.values(groupedData);
    const total = buArray.reduce((sum, item: any) => sum + item.value, 0);

    // Add colors and percentages
    const colors = ["#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#06b6d4"];

    return buArray.map((item: any, index) => ({
      ...item,
      percentage: total > 0 ? ((item.value as number) / total) * 100 : 0,
      color: colors[index % colors.length]
    }));
  }, [data]);

  const buData = processedBUData;
  const total = buData.reduce((sum, item) => sum + item.value, 0);

  const pieChartOptions = {
    chart: {
      type: 'pie',
      backgroundColor: 'transparent',
      height: 400,
    },
    title: {
      text: 'Distribution by Business Unit - Scope 1 Emissions',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    subtitle: {
      text: `Total: ${total.toFixed(2)} tCO₂e | ${buData.length} Business Units`,
      style: {
        fontSize: '14px',
        color: '#6b7280'
      }
    },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        dataLabels: {
          enabled: true,
          format: '<b>{point.name}</b>: {point.percentage:.1f}%',
          style: {
            fontSize: '12px'
          }
        },
        showInLegend: true
      }
    },
    series: [{
      name: 'Emissions',
      colorByPoint: true,
      showInLegend: true,
      data: buData.map(item => ({
        name: item.name,
        y: item.value,
        color: item.color,
        showInLegend: true
      }))
    }],
    tooltip: {
      formatter: function () {
        return `<b>${this.point.name}</b><br/>
                Emissions: <b>${this.y.toFixed(2)} tCO₂e</b><br/>
                Percentage: <b>${this.percentage.toFixed(1)}%</b>`;
      }
    },
    legend: {
      enabled: true,
      align: 'right',
      verticalAlign: 'middle',
      layout: 'vertical',
      itemStyle: {
        fontSize: '12px'
      },
      useHTML: false
    },
    credits: {
      enabled: false
    }
  };

  const barChartOptions = {
    chart: {
      type: 'bar',
      backgroundColor: 'transparent',
      height: 300,
    },
    title: {
      text: null
    },
    xAxis: {
      categories: buData.map(item => item.name),
      title: {
        text: 'Business Unit'
      }
    },
    yAxis: {
      title: {
        text: 'Emissions (tCO₂e)'
      }
    },
    series: [{
      name: 'Scope 1 Emissions',
      data: buData.map(item => ({
        y: item.value,
        color: item.color
      })),
      dataLabels: {
        enabled: true,
        format: '{y:.1f}'
      }
    }],
    plotOptions: {
      bar: {
        borderRadius: 4,
        dataLabels: {
          enabled: true
        }
      }
    },
    tooltip: {
      formatter: function () {
        const percentage = (this.y / total * 100).toFixed(1);
        const bu = buData.find(unit => unit.name === this.x);
        return `<b>${this.x}</b><br/>
                Emissions: <b>${this.y.toFixed(2)} tCO₂e</b><br/>
                Percentage: <b>${percentage}%</b><br/>`;
      }
    },
    legend: {
      enabled: true,
      align: 'center',
      verticalAlign: 'bottom',
      layout: 'horizontal',
      itemStyle: {
        fontSize: '12px'
      }
    },
    credits: {
      enabled: false
    }
  };

  const [viewType, setViewType] = useState<'pie' | 'bar'>('pie');

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="mb-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Distribution by Business Unit - Scope 1 Emissions</h3>
            <p className="text-sm text-gray-600">Breakdown of direct emissions by business unit</p>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setViewType('pie')}
              className={`px-3 py-1 text-sm rounded ${viewType === 'pie'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
            >
              Pie Chart
            </button>
            <button
              onClick={() => setViewType('bar')}
              className={`px-3 py-1 text-sm rounded ${viewType === 'bar'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
            >
              Bar Chart
            </button>
          </div>
        </div>
      </div>

      <HighchartsReact
        key={`scope1-chart-${viewType}`}
        highcharts={Highcharts}
        options={viewType === 'pie' ? pieChartOptions : barChartOptions}
      />

      {/* Summary Cards */}
     

      {/* No Data Message */}
      {buData.length === 0 && (
        <div className="mt-4 text-center py-8">
          <div className="text-gray-500">
            No data available for Business Unit distribution.
           
          </div>
        </div>
      )}

    </div>
  );
};

export default Scope1DistributionByBUChart;
