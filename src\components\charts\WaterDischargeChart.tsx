import React, { useEffect, useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { EnvironmentDrilldownChart } from "./EnvironmentDrilldownChart";
import waterMonthlyDrilldownData from "@/data/water-monthly-drilldown-data.json";
import {
  EnvironmentLocationFilter,
  EnvironmentLocation,
} from "./EnvironmentLocationFilter";
import axios from "axios";

interface WaterDischargeChartProps {
  region: string;
  suppliersData: any[];
  enableDrilldown?: boolean;
  useLocationFilter?: boolean; // true for Environment tab, false for Overview tab
}

const regionOptions = ["India", "Indonesia", "United Kingdom", "Global"];

// Sample data: region -> [FY-2024, FY-2025, FY-2026]
const regionData: Record<string, number[]> = {
  India: [9000, 8800, 8600],
  Indonesia: [7000, 6900, 6800],
  "United Kingdom": [4000, 3950, 3900],
  Global: [20000, 19800, 19600],
};

export const WaterDischargeChart: React.FC<WaterDischargeChartProps> = ({
  region,
  suppliersData,
  enableDrilldown = false,
  useLocationFilter = false,
}) => {
  const [location, setLocation] = useState<EnvironmentLocation>({
    country: region || "Global",
    region: "",
    businessUnit: "",
  });
  const [selectedRegion, setSelectedRegion] = useState(region || "Global");
  const [drilldown, setDrilldown] = useState<{
    isActive: boolean;
    year: string;
  } | null>(null);
  // Data selection logic: prefer businessUnit > region > country
  let data = useLocationFilter
    ? regionData[location.country] || regionData["Global"]
    : regionData[selectedRegion] || regionData["Global"];
  // (Extend here if you add region/businessUnit-level data)

  const [supplierData, setSupplierData] = useState<any[]>(suppliersData);
  const [locationData, setLocationData] = useState<any[]>([]);
  const [scopeData, setScopeData] = useState<any>({});

  const fetchLocationData: any = async (location: string) => {
    try {
      const response = await axios.get(
        "https://api.eisqr.com/user-profiles/289/location-ones?filter=%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationTwos%22%2C%22scope%22%3A%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationThrees%22%7D%5D%7D%7D%5D%7D"
      );

      return response.data;
    } catch (err) {
      console.warn("error:", err);
    }
  };

  const extractAllLocationIds = (locationOne: any): number[] => {
    const ids: number[] = [];
    ids.push(locationOne.id);

    locationOne.locationTwos?.forEach((locationTwo: any) => {
      if (locationTwo.id) ids.push(locationTwo.id);

      locationTwo.locationThrees?.forEach((locationThree: any) => {
        if (locationThree.id) ids.push(locationThree.id);
      });
    });

    return ids;
  };

  useEffect(() => {
    (async () => {
      setSelectedRegion("Global");
      const locationData = await fetchLocationData(selectedRegion);
      setLocationData(locationData);

      setSupplierData(suppliersData);
    })();
  }, []);

  useEffect(() => {
    (async () => {
      let locationIndvData =
        selectedRegion == "Global"
          ? locationData
          : locationData.filter((item) => item.name == selectedRegion)[0];

      const allLocationIds = extractAllLocationIds(locationIndvData);

      let finalData =
        selectedRegion == "Global"
          ? supplierData
          : supplierData.filter((item) =>
              allLocationIds.includes(item.locationId)
            );

      let consumption = { 2024: 0, 2025: 0, 2026: 0 };

      finalData.forEach((item) => {
        const period = item.reporting_period;
        let fiscalYear = "";

        if (period.includes("to")) {
          const match = period.match(/Apr-(\d{4}) to Mar-(\d{4})/);
          if (match) fiscalYear = match[2];
        } else {
          const match = period.match(/([A-Za-z]{3}|\d{2})-(\d{4})/);
          if (match) {
            const monthStr = match[1];
            const year = +match[2];
            const monthMap: Record<string, number> = {
              Jan: 0,
              Feb: 1,
              Mar: 2,
              Apr: 3,
              May: 4,
              Jun: 5,
              Jul: 6,
              Aug: 7,
              Sep: 8,
              Oct: 9,
              Nov: 10,
              Dec: 11,
            };
            const monthNum = isNaN(+monthStr)
              ? monthMap[monthStr]
              : +monthStr - 1;
            fiscalYear =
              monthNum >= 3 ? (year + 1).toString() : year.toString();
          }
        }

        if (!fiscalYear) return;

        consumption[fiscalYear] += +item.computedValue || 0;
      });

      data[selectedRegion] = Object.values(consumption).map(
        (item) => +item.toFixed(2)
      );

      setScopeData(data[selectedRegion]);
    })();
  }, [selectedRegion, location]);

  const handleDrilldown = (yearIndex: number) => {
    if (!enableDrilldown) return;
    const years = ["2024", "2025", "2026"];
    setDrilldown({ isActive: true, year: years[yearIndex] });
  };

  const handleBack = () => setDrilldown(null);

  // if (enableDrilldown && drilldown?.isActive) {
  //   return (
  //     <EnvironmentDrilldownChart
  //       metricType="discharge"
  //       region={location.country}
  //       year={drilldown.year}
  //       onBack={handleBack}
  //       monthlyDrilldownData={waterMonthlyDrilldownData.monthlyDrilldownData}
  //     />
  //   );
  // }

  const options: Highcharts.Options = {
    chart: {
      type: "column",
      backgroundColor: "#fff",
      style: { fontFamily: "inherit" },
    },
    title: { text: undefined },
    xAxis: {
      categories: ["FY-2024", "FY-2025", "FY-2026"],
      title: { text: "Fiscal Year" },
      labels: { style: { fontSize: "14px", color: "#334155" } },
    },
    yAxis: {
      min: 0,
      title: {
        text: "Water Discharge (Kilo Liters, KL)",
        style: { fontSize: "14px", color: "#334155" },
      },
      labels: {
        formatter: function () {
          return this.value + "";
        },
        style: { fontSize: "13px", color: "#64748b" },
      },
      gridLineColor: "#e5e7eb",
    },
    legend: { enabled: false },
    tooltip: {
      pointFormat: "<b>{point.y} KL</b>",
    },
    plotOptions: {
      column: {
        borderRadius: 6,
        dataLabels: {
          enabled: true,
          format: "{y}",
          style: { fontWeight: "bold", color: "#334155" },
        },
        point: {
          events: {
            click: function () {
              if (!enableDrilldown) return;
              handleDrilldown(this.x);
            },
          },
        },
        cursor: enableDrilldown ? "pointer" : "default",
      },
    },
    series: [
      {
        type: "column",
        name: "Water Discharge",
        data: scopeData,
        color: "#10b981",
      },
    ],
    credits: { enabled: false },
  };

  return (
    <div className="bg-white rounded-lg shadow p-4">
      {useLocationFilter ? (
        <EnvironmentLocationFilter value={location} onChange={setLocation} />
      ) : (
        <div className="mb-4 flex items-center space-x-3">
          <label
            htmlFor="water-discharge-region-select"
            className="font-medium text-gray-700"
          >
            Country/Region:
          </label>
          <select
            id="water-discharge-region-select"
            value={selectedRegion}
            onChange={(e) => setSelectedRegion(e.target.value)}
            className="border border-gray-300 rounded px-3 py-1.5 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-400 bg-white shadow-sm"
          >
            {Object.keys(regionData).map((opt) => (
              <option key={opt} value={opt}>
                {opt}
              </option>
            ))}
          </select>
        </div>
      )}
      {enableDrilldown && (
        <div className="mb-2 text-sm text-gray-600">
          💡 Click on any bar to view monthly breakdown
        </div>
      )}
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};
